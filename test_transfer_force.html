<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Transfer and Force Display Features</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
            border: none;
        }
        button:hover {
            background-color: #0056b3;
        }
        .force-btn {
            background-color: #dc3545;
        }
        .force-btn:hover {
            background-color: #c82333;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .ticket-row:hover {
            background-color: #f5f5f5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Terminal Transfer and Force Display Test</h1>
        
        <!-- Force Transfer Section -->
        <div class="section">
            <h2>1. Force Transfer Ticket</h2>
            <p>Transfer a ticket to another terminal regardless of service assignment.</p>
            
            <div class="form-group">
                <label for="transfer_transaction_id">Transaction ID:</label>
                <input type="number" id="transfer_transaction_id" placeholder="Enter transaction ID">
            </div>
            
            <div class="form-group">
                <label for="target_terminal">Target Terminal Window Number:</label>
                <input type="number" id="target_terminal" placeholder="Enter terminal window number">
            </div>
            
            <button onclick="normalTransfer()">Normal Transfer</button>
            <button onclick="forceTransfer()" class="force-btn">Force Transfer</button>
            
            <div id="transfer_result" class="result" style="display: none;"></div>
        </div>
        
        <!-- Force Display Section -->
        <div class="section">
            <h2>2. Force Display All Waiting Tickets</h2>
            <p>Display all waiting tickets regardless of service assignment to terminal.</p>
            
            <button onclick="getAllWaitingTickets()">Get Normal Waiting Tickets</button>
            <button onclick="getForceDisplayTickets()" class="force-btn">Force Display All Tickets</button>
            <button onclick="serveWithForceDisplay()" class="force-btn">Serve Next (Force Display)</button>
            
            <div id="display_result" class="result" style="display: none;"></div>
            
            <div id="tickets_table" style="display: none;">
                <h3>Waiting Tickets:</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Ticket #</th>
                            <th>Service</th>
                            <th>Client Name</th>
                            <th>Waiting Time</th>
                            <th>Terminal ID</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody id="tickets_tbody">
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Instructions Section -->
        <div class="section">
            <h2>3. Instructions</h2>
            <ul>
                <li><strong>Normal Transfer:</strong> Uses existing logic - only transfers if target terminal supports the service</li>
                <li><strong>Force Transfer:</strong> Transfers ticket regardless of service assignment</li>
                <li><strong>Normal Display:</strong> Shows only tickets for services assigned to current terminal</li>
                <li><strong>Force Display:</strong> Shows ALL waiting tickets regardless of service assignment</li>
                <li><strong>Serve with Force Display:</strong> Serves next ticket from all waiting tickets, not just assigned services</li>
            </ul>
        </div>
    </div>

    <script>
        // Base URL - adjust this to match your CodeIgniter installation
        const BASE_URL = 'http://localhost/WamNextGen/'; // Change this to your actual base URL
        
        function normalTransfer() {
            const transactionId = document.getElementById('transfer_transaction_id').value;
            const targetTerminal = document.getElementById('target_terminal').value;
            
            if (!transactionId || !targetTerminal) {
                showResult('transfer_result', 'Please fill in both Transaction ID and Target Terminal', 'error');
                return;
            }
            
            fetch(`${BASE_URL}C_terminal/do_transfer_ticket/${transactionId}/${targetTerminal}`)
                .then(response => response.text())
                .then(data => {
                    showResult('transfer_result', `Normal Transfer Result: ${data}`, 'success');
                })
                .catch(error => {
                    showResult('transfer_result', `Error: ${error.message}`, 'error');
                });
        }
        
        function forceTransfer() {
            const transactionId = document.getElementById('transfer_transaction_id').value;
            const targetTerminal = document.getElementById('target_terminal').value;
            
            if (!transactionId || !targetTerminal) {
                showResult('transfer_result', 'Please fill in both Transaction ID and Target Terminal', 'error');
                return;
            }
            
            fetch(`${BASE_URL}C_terminal/do_force_transfer_ticket/${transactionId}/${targetTerminal}`)
                .then(response => response.text())
                .then(data => {
                    showResult('transfer_result', `Force Transfer Result: ${data}`, 'success');
                })
                .catch(error => {
                    showResult('transfer_result', `Error: ${error.message}`, 'error');
                });
        }
        
        function getAllWaitingTickets() {
            // This would call the normal serve_ticket method to see regular waiting tickets
            showResult('display_result', 'Normal waiting tickets display - would show only assigned services', 'success');
        }
        
        function getForceDisplayTickets() {
            fetch(`${BASE_URL}C_terminal/get_all_waiting_tickets`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        showResult('display_result', `Error: ${data.error}`, 'error');
                    } else {
                        showResult('display_result', `Found ${data.waiting_transactions.length} waiting tickets (Force Display)`, 'success');
                        displayTicketsTable(data.waiting_transactions);
                    }
                })
                .catch(error => {
                    showResult('display_result', `Error: ${error.message}`, 'error');
                });
        }
        
        function serveWithForceDisplay() {
            fetch(`${BASE_URL}C_terminal/serve_ticket_force_display/0`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        showResult('display_result', `Error: ${data.error}`, 'error');
                    } else {
                        showResult('display_result', `Served ticket: ${data.ticket_num} (Force Display Mode)`, 'success');
                    }
                })
                .catch(error => {
                    showResult('display_result', `Error: ${error.message}`, 'error');
                });
        }
        
        function showResult(elementId, message, type) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.innerHTML = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
        
        function displayTicketsTable(tickets) {
            const tableDiv = document.getElementById('tickets_table');
            const tbody = document.getElementById('tickets_tbody');
            
            tbody.innerHTML = '';
            
            if (tickets.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6">No waiting tickets found</td></tr>';
            } else {
                tickets.forEach(ticket => {
                    const row = document.createElement('tr');
                    row.className = 'ticket-row';
                    row.innerHTML = `
                        <td>${ticket.ticket_num || 'N/A'}</td>
                        <td>${ticket.service_name || 'N/A'}</td>
                        <td>${ticket.client_name || 'N/A'}</td>
                        <td>${ticket.calculated_waiting_time || 'N/A'}</td>
                        <td>${ticket.terminal_id || 'N/A'}</td>
                        <td>${ticket.status === 0 ? 'Waiting' : 'Other'}</td>
                    `;
                    tbody.appendChild(row);
                });
            }
            
            tableDiv.style.display = 'block';
        }
    </script>
</body>
</html>
