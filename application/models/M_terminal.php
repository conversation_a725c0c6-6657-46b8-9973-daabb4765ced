<?php

class M_terminal extends CI_model
{

    public function get_internal_area_all_data($branch)
    {
        $this->db->select('branch_internal_area.*,Branch.EnglishBranchName');
        $this->db->join('Branch', 'branch_internal_area.BranchID = Branch.BranchID');
        $query = $this->db->get_where('branch_internal_area', ['branch_internal_area.BranchID' => $branch]);
        $arr = [];
        foreach ($query->result() as $row) {
            $arr[] = [
                'id' => $row->AreaID,
                'branch_id' => $row->BranchID,
                'branch_name' => $row->EnglishBranchName,
                'en_name' => $row->EnglishAreaName,
                'ar_name' => $row->ArabicAreaName,
            ];
        }
        return $arr;
    }

    public function get_internal_area_data_with_id($id)
    {
        $query = $this->db->get_where('branch_internal_area', array('AreaID' => $id));
        $row = $query->row();
        return $row;
    }

    public function insert_internal_area_data($en_name, $ar_name)
    {
        $branch_id = $this->session->userdata('branch_id');

        $data = array(
            'EnglishAreaName' => $en_name,
            'ArabicAreaName' => $ar_name,
            'BranchID' => $branch_id
        );

        $this->db->insert('branch_internal_area', $data);
        $inserted = $this->db->insert_id();
        if ($this->db->dbdriver == 'sqlsrv') {
            $this->db->query('UPDATE "branch_internal_area" SET "ArabicAreaName" = N\'' . $ar_name . '\' WHERE "AreaID" = ' . $inserted);
        } else {
            $this->db->query('UPDATE branch_internal_area SET ArabicAreaName = \'' . $ar_name . '\' WHERE AreaID = ' . $inserted);
        }
    }
    public function edit_internal_area_data($en_name, $ar_name, $id)
    {
        $data = array(
            'EnglishAreaName' => $en_name,
            'ArabicAreaName' => $ar_name
        );

        $this->db->where('AreaID', $id);
        $this->db->update('branch_internal_area', $data);
        if ($this->db->dbdriver == 'sqlsrv') {
            $this->db->query('UPDATE "branch_internal_area" SET "ArabicAreaName" = N\'' . $ar_name . '\' WHERE "AreaID" = ' . $id);
        } else {
            $this->db->query('UPDATE branch_internal_area SET ArabicAreaName = \'' . $ar_name . '\' WHERE AreaID = ' . $id);
        }
    }

    public function delete_internal_area_record($id)
    {
        $this->db->delete('branch_internal_area', array('AreaID' => $id));
    }

    public function get_branches()
    {
        $this->db->select('BranchID,EnglishBranchName');
        $query = $this->db->get_where('Branch', array('deleted' => 0));
        $arr = array();
        foreach ($query->result() as $row) {
            $arr[] = $row;
        }
        return $arr;
    }

    public function get_branches_2($BranchAreaID)
    {
        $access_all = get_p("branch_group_lvl", "a");
        $this->db->select('b.BranchID,b.EnglishBranchName');
        $this->db->where("b.BranchAreaID", $BranchAreaID);
        $this->db->where("b.deleted", 0);
        if ($this->session->userdata('User_type') == 'User') {
            if ($access_all == 3) {
                $this->db->where_in("b.BranchID", get_branches_access());
            }
        }
        $query = $this->db->get('Branch b');
        return $query->result();
    }

    public function get_roles()
    {
        $this->db->select('RoleID,RoleName');
        $query = $this->db->get('Role');
        $arr = array();
        foreach ($query->result() as $row) {
            $arr[] = $row;
        }
        return $arr;
    }

    public function insert_data($win_no, $dis_add, $dis_active, $dis_comm, $term_comm, $dir, $floor, $calling, $comLayer = [], $mqtt_ch = '', $mqtt_terminal_id = '', $mqtt = [], $poe = [], $audio_controller = [], $branch_internal_area, $branch_id, $extra_calling)
    {
        $data = array(
            'window_no' => $win_no,
            'display_address' => $dis_add,
            'display_active' => $dis_active,
            'display_comm' => $dis_comm,
            'term_comm' => $term_comm,
            'dir' => $dir,
            'floor' => $floor,
            'calling_technique' => $calling,
            'calling_technique_extra' => $extra_calling,
            'comLayer' => serialize($comLayer),
            'mqtt_ch' => $mqtt_ch,
            'mqtt_terminal_id' => $mqtt_terminal_id,
            'mqtt' => serialize($mqtt),
            'poe' => serialize($poe),
            'audio_controller' => serialize($audio_controller),
            'branch_id' => $branch_id,
            'BranchIntAreaID' => $branch_internal_area,
            'key' => get_key('terminal'),
            'status' => 0,
        );
        $this->db->insert('terminal', $data);
    }

    public function get_all_data()
    {
        $this->db->select('terminal.id,terminal.window_no,terminal.display_address,terminal.display_active,'
            . 'terminal.display_comm,terminal.term_comm,terminal.status,terminal,terminal.comLayer,terminal.mqtt,terminal.poe,terminal.audio_controller'
            . ',terminal.dir,terminal.BranchIntAreaID,Branch.EnglishBranchName,branch_internal_area.EnglishAreaName');
        $this->db->from('terminal');
        $this->db->join('Branch', 'terminal.branch_id = Branch.BranchID');
        $this->db->join('branch_internal_area', 'branch_internal_area.AreaID = terminal.BranchIntAreaID', 'left');
        $this->db->where(array('Branch.deleted' => 0, 'terminal.deleted' => 0));
        $query = $this->db->get();

        $arr = array();

        foreach ($query->result() as $row) {
            $dis_active = $row->display_active == 0 ? 'No' : 'YES';
            $dis_comm = $row->display_comm == 0 ? 'No' : 'YES';
            $term_comm = $row->term_comm == 0 ? 'No' : 'YES';

            $arr[] = array('id' => $row->id,
                'w_no' => $row->window_no,
                'area_name' => $row->EnglishAreaName,
                'dis_add' => $row->display_address,
                'dis_active' => $dis_active,
                'dis_comm' => $dis_comm,
                'term_comm' => $term_comm,
                'dir' => $row->dir,
                'comLayer' => unserialize($row->comLayer),
                'mqtt' => unserialize($row->mqtt),
                'poe' => unserialize($row->poe),
                'audio_controller' => unserialize($row->audio_controller),
                'branch_name' => $row->en_name,
                'BranchIntAreaID' => $row->BranchIntAreaID,
                'agent_username' => $current_agent_username,
                'terminal_has_agent' => $current_agent_id,
                'status' => $row->status,
            );
        }

        return $arr;
    }

    public function get_data_with_id($id)
    {
        $query = $this->db->get_where('terminal', array('id' => $id));
        $row = $query->row();
        $arr['row'] = $row;
        $arr['ip_digits'] = $row->display_address;
        return $arr;
    }

    public function edit_data($win_no, $dis_add, $dis_active, $dis_comm, $term_comm, $dir, $floor, $calling, $extra_calling, $comLayer = [], $mqtt_ch = '', $mqtt_terminal_id = '', $mqtt = [], $poe = [], $audio_controller = [], $branch_internal_area, $branch_id, $id)
    {
        $data = array(
            'window_no' => $win_no,
            'display_address' => $dis_add,
            'display_active' => $dis_active,
            'display_comm' => $dis_comm,
            'term_comm' => $term_comm,
            'dir' => $dir,
            'floor' => $floor,
            'calling_technique' => $calling,
            'calling_technique_extra' => $extra_calling,
            'comLayer' => serialize($comLayer),
            'mqtt_ch' => $mqtt_ch,
            'mqtt_terminal_id' => $mqtt_terminal_id,
            'mqtt' => serialize($mqtt),
            'poe' => serialize($poe),
            'audio_controller' => serialize($audio_controller),
            'branch_id' => $branch_id,
            'BranchIntAreaID' => $branch_internal_area,
        );
        $this->db->where('id', $id);
        $this->db->update('terminal', $data);
    }

    public function delete_record($id)
    {
        $data = array('deleted' => 1);
        $this->db->where('id', $id);
        $this->db->update('terminal', $data);
    }

    public function unassign_agent($terminal_has_agent_id)
    {
        $data = array('time_to' => date('H:i:s'));
        $this->db->where('id', $terminal_has_agent_id);
        $this->db->update('terminal_has_agent', $data);
    }

    public function assign_agent($terminal_id, $user_id)
    {
        $data = array(
            'terminal_id' => $terminal_id,
            'date' => date("Y-m-d"),
            'time_from' => date('H:i:s'),
            'users_id' => $user_id,
        );

        $this->db->insert('terminal_has_agent', $data);
    }

    public function get_available_agents($terminal_id)
    {
        $this->db->select('users_id');
        $this->db->from('terminal_has_agent');
        $this->db->where(array('date' => date("Y-m-d"), 'time_to' => null));
        $busy_agents_qu = $this->db->get();
        $busy_agents_arr = array();
        foreach ($busy_agents_qu->result() as $value) {
            $busy_agents_arr[] = $value->users_id;
        }
        $ter_branch_id = $this->db->get_where('terminal', array('id' => $terminal_id))->row()->branch_id;
        $this->db->select('UserID,UserName');
        $this->db->from('Users');
        $this->db->where('branch_id', $ter_branch_id);
        if (!empty($busy_agents_arr)) {
            $this->db->where_not_in('id', $busy_agents_arr);
        }
        $qu = $this->db->get();

        return $qu->result();
    }

    public function update_terminal_status($terminal_id, $status)
    {
        // update idle state when agent takes break or return
        $arr = array('idle_state' => $status);
        $this->db->where('id', $terminal_id);
        $this->db->update('terminal', $arr);
    }

    public function update_terminal_status2($terminal_id, $status)
    {
        // update state when agent login or logout
        $arr = array('status' => $status);
        $this->db->where('id', $terminal_id);
        $this->db->update('terminal', $arr);
    }

    public function get_user_terminals($branches)
    {
        $this->db->where_in('branch_id', $branches);
        $query = $this->db->get('terminal');
        return $query->result();
    }

    public function my_get_all_data($branch, $ignored_ids = null)
    {
        $this->db->select('terminal.id,calling_technique,calling_technique_extra,terminal.key,terminal.window_no,terminal.display_address,terminal.display_active,'
            . 'terminal.display_comm,terminal.term_comm,terminal.idle_state,terminal.status,terminal.floor'
            . ',terminal.dir,terminal.order_num,terminal.BranchIntAreaID,Branch.EnglishBranchName,branch_internal_area.EnglishAreaName');
        $this->db->from('terminal');
        $this->db->join('Branch', 'terminal.branch_id = Branch.BranchID');
        $this->db->join('branch_internal_area', 'branch_internal_area.AreaID = terminal.BranchIntAreaID', 'left');
        $this->db->where(array('Branch.deleted' => 0, 'terminal.deleted' => 0));
        $this->db->where('terminal.branch_id', $branch);
        if (!empty($ignored_ids)) {
            $this->db->where_not_in('terminal.id', $ignored_ids);
        }
        $this->db->order_by('terminal.window_no');
        $query = $this->db->get();
        $arr = array();
        foreach ($query->result() as $row) {
            $dis_active = $row->display_active == 0 ? 'No' : 'YES';
            $dis_comm = $row->display_comm == 0 ? 'No' : 'YES';
            $term_comm = $row->term_comm == 0 ? 'No' : 'YES';
            $arr[] = array(
                'id' => $row->id,
                'BranchIntAreaID' => $row->BranchIntAreaID,
                'area_name' => $row->EnglishAreaName,
                'w_no' => $row->window_no,
                'dis_add' => $row->display_address,
                'dis_active' => $dis_active,
                'dis_comm' => $dis_comm,
                'term_comm' => $term_comm,
                'dir' => $row->dir,
                'order_num' => $row->order_num,
                'branch_name' => $row->EnglishBranchName,
                'status' => $row->status,
                'idle_state' => $row->idle_state,
                'floor' => $row->floor,
                'key' => $row->key,
                'calling_technique' => $row->calling_technique,
                'calling_technique_extra' => $row->calling_technique_extra,
            );
        }
        return $arr;
    }

    public function get_branch_waiting_areas($branch_id, $id = null)
    {
        return $this->db->get_where('waiting_areas', ['branch_id' => $branch_id, 'id !=' => $id])->result();
    }

    public function get_user_branches($uid)
    {
        $this->db->select('branch_id');
        $this->db->where(array('id' => $uid));
        $query = $this->db->get('Users');
        return $query->row();
    }

    public function get_branches_details($branch)
    {
        $this->db->where('BranchID', $branch);
        $query = $this->db->get('Branch');
        return $query->result();
    }

    public function repeated_win_or_display($branch_id, $win_num, $display_address, $internal_area)
    {
        $num = $this->db->get_where('terminal', array('window_no' => $win_num, 'branch_id' => $branch_id, 'BranchIntAreaID' => $internal_area, 'deleted' => 0))->num_rows();
        if ($num > 0) {
            return $this->lang->line('win_no_exists_prev');
        }
        $num = $this->db->get_where('terminal', array('display_address' => $display_address, 'branch_id' => $branch_id, 'deleted' => 0))->num_rows();
        if ($num > 0) {
            return $this->lang->line('display_add_exists_prev');
        }
        return false;
    }

    public function repeated_win_or_display_edit($branch_id, $win_num, $display_address, $internal_area, $edited_id)
    {
        $num = $this->db->get_where('terminal', array('window_no' => $win_num, 'id !=' => $edited_id, 'branch_id' => $branch_id, 'BranchIntAreaID' => $internal_area, 'deleted' => 0))->num_rows();
        if ($num > 0) {
            return $this->lang->line('win_no_exists_prev');
        }
        $num = $this->db->get_where('terminal', array('display_address' => $display_address, 'id !=' => $edited_id, 'branch_id' => $branch_id, 'deleted' => 0))->num_rows();
        if ($num > 0) {
            return $this->lang->line('display_add_exists_prev');
        }
        return false;
    }

    public function get_branch_of_terminal($ter_id)
    {
        $this->db->select('branch_id');
        return $this->db->get_where('terminal', array('id' => $ter_id))->row();
    }

    public function get_branch_name($branch_id)
    {
        $this->db->select('EnglishBranchName');
        return $this->db->get_where('Branch', array('BranchID' => $branch_id))->row();
    }

    public function get_branch_terminals($branch_id, $terminals = [])
    {
        $this->db->select('id,window_no,comLayer,mqtt,poe,audio_controller');
        $this->db->from('terminal');
        if (!empty($terminals)) {
            $this->db->where_not_in('window_no', $terminals);
        }
        $this->db->where('branch_id', $branch_id);
        $this->db->where('status', 0);
        $this->db->where('deleted', 0);
        return $this->db->get()->result();
    }

    public function get_branch_all_terminals($branch_id, $terminals = [])
    {
        $this->db->select('id,window_no,comLayer,mqtt,poe,audio_controller');
        $this->db->from('terminal');
        if (!empty($terminals)) {
            $this->db->where_not_in('window_no', $terminals);
        }
        $this->db->where('branch_id', $branch_id);
        $this->db->where('deleted', 0);
        return $this->db->get()->result();
    }

    public function get_branch_terminals_array($branch_id)
    {
        $this->db->select('id,window_no,comLayer,mqtt,poe,audio_controller');
        $this->db->from('terminal');
        $this->db->where(['branch_id' => $branch_id, 'status' => 0, 'deleted' => 0]);
        return $this->db->get()->result_array();
    }

    public function get_branch_empty_terminals($branch_id)
    {
        $this->db->select('id,window_no,comLayer,mqtt,poe,audio_controller');
        $this->db->from('terminal');
        $this->db->where(['branch_id' => $branch_id, 'status' => 0, 'deleted' => 0]);
        return $this->db->get()->result();
    }

    public function get_branch_terminal($branch = null, $terminal = null)
    {
        $this->db->select('terminal.*');
        $this->db->from('terminal');
        $this->db->where('branch_id', $branch ? $branch : $this->session->userdata('branch_id'));
        $this->db->where('window_no', $terminal ? $terminal : $this->session->userdata('terminal_window_no'));
        $this->db->where('deleted', 0);
        return $this->db->get()->row();
    }

    public function get_terminal_id($window_no, $branch_id)
    {
        $this->db->select('id,key');
        $q = $this->db->get_where('terminal', array('window_no' => $window_no, 'branch_id' => $branch_id, 'deleted' => 0));
        return $q->row();
    }

    public function get_terminal_has_services($terminal_id)
    {
        /*$terminal = $this->db->get_where('terminal', array('id' => $terminal_id))->row();
        $this->db->select('service_id');
        $this->db->from('terminal_has_service');
        $this->db->where('terminal_key', $terminal->key);
        return $this->db->get()->result();*/
		$terminal = $this->db->get_where('terminal', array('id' => $terminal_id))->row();
        $this->db->select('ts.service_id,s.id,s.service_name,s.can_choose_agent');
        $this->db->join('service s', 's.id=ts.service_id AND s.deleted=0');
        return $this->db->get_where('terminal_has_service ts', array('ts.terminal_key' => $terminal->key))->result();
    }

    public function get_service_num($service_id)
    {
        $this->db->select('service_num');
        $this->db->from('template_vr_services');
        $this->db->where('branch_id', $this->session->userdata('branch_id'));
        $this->db->where('service_id', $service_id);
        return $this->db->get()->row();
    }

    public function get_current_transactions()
    {

        $this->db->select('transactions.*');
        $this->db->from('transactions');
        $this->db->order_by('entering_time');
        $this->db->where('terminal_id', $this->session->userdata('terminal_window_no'));
        $this->db->where('branch_id', $this->session->userdata('branch_id'));
        $this->db->where('status', 1);
        $transactions = $this->db->get()->result();

        foreach ($transactions as $row) {
            if ($row->resuming_time != null) {
                $last_period_serve_time = $this->get_datetime_diff($row->resuming_time, null);

                $secs = strtotime($row->serving_time) - strtotime("00:00:00");
                $row->calculated_serving_time = date('H:i:s', strtotime($last_period_serve_time) + $secs);
            } else {
                $row->calculated_serving_time = $this->get_datetime_diff($row->waiting_date_time, null);
            }
        }
        return $transactions;
    }

    public function get_waiting_transactions($services_num_arr, $calling_technique = [], $one = false, $branch = null, $terminal = null, $agent = null, $ticket_num = null,$choose_agent = false, $force_all = false)
    {
        $settings = $this->db->select('agent_tickets')->get('OrganisationGlobalSettings')->row();
        if (!$services_num_arr && empty($settings->agent_tickets) && !$force_all) {
            return [];
        }

        if ($this->db->dbdriver == 'sqlsrv') {
            $this->db->select(
                't.*,' // Transaction info
                . 'srv.arabic_service_name as service_name,' // Service name
                . 'esc.counter,' // Escalate counter
                . 'wt.reservation_priority,' // Work transaction reservation priority
                . 'wt.segment_id,' // Work transaction segment id
                . 'wt.enter_time,' // Work transaction enter time
                . 'ISNULL ("t_r_1"."segment_prio_level","prio_1"."prioity_level") as priority,' // Priority
                . 'ISNULL ("t_r_2"."segment_prio_level","prio_2"."prioity_level") as reservation_priority,' // Reservation priorty
                . 'prio_1.id as priority_id' // Priorty id
            );
        } else {
            $this->db->select(
                't.*,' // Transaction info
                . 'srv.arabic_service_name as service_name,' // Service name
                . 'esc.counter as counter,' // Escalate counter
                . 'wt.reservation_priority as reservation_priority,' // Work transaction reservation priority
                . 'wt.segment_id as segment_id,' // Work transaction segment id
                . 'wt.enter_time as enter_time,' // Work transaction enter time
                . 'COALESCE (t_r_1.segment_prio_level,prio_1.prioity_level) as priority,' // Priority
                . 'COALESCE (t_r_2.segment_prio_level,prio_2.prioity_level) as reservation_priority,' // Reservation priorty
                . 'prio_1.id as priority_id' // Priorty id
            );
        }
        if ($calling_technique[1] && !$calling_technique[2]) {
            // Order by FCFS
            $this->db->order_by('wt.enter_time');
        } elseif ($calling_technique[2]) {
            // Priority based
            $this->db->order_by('wt.segment_id', 'DESC');
            $this->db->order_by('wt.enter_time');
        }
        $this->db->from('transactions t');

        $this->db->join('service srv', 'srv.id = t.service_id');
        
        $this->db->join('work_transactions wt', 'wt.id = t.work_transaction_id');

        $this->db->join('escalate_manual esc', 'esc.transaction_id = t.id', 'left');

        // Inner Join workflow_ticket_ranges and priorty for transaction
        $this->db->join('workflow_ticket_ranges t_r_1', 't_r_1.segment_id=wt.segment_id and t_r_1.workflow_id=wt.workflow_id');
        $this->db->join('priority prio_1', 'prio_1.key = t_r_1.segment_key and t_r_1.segment_key!=""');

        // Left join workflow_ticket_ranges and priorty for reservation
        $this->db->join('workflow_ticket_ranges t_r_2', 't_r_2.id=wt.rservation_segment_id', 'left');
        $this->db->join('priority prio_2', 'prio_2.key = t_r_2.segment_key and t_r_2.segment_key!=""', 'left');

        $this->db->where('(t.terminal_id = 0 OR t.terminal_id = ' . ($terminal ? $terminal : $this->session->userdata('terminal_window_no')) . ')');
        if (!empty($settings->agent_tickets) && $choose_agent) {
            $this->db->where('(t.agent_id = ' . ($agent ? $agent : $this->session->userdata('login')) . ')');
        } else {
            // Only filter by service if not forcing all tickets
            if (!$force_all && !empty($services_num_arr)) {
                $this->db->where_in('t.service_id', $services_num_arr);
            }
            $this->db->where('(t.agent_id = 0 OR t.agent_id = ' . ($agent ? $agent : ($this->session->userdata('login') ? $this->session->userdata('login') : 0)) . ')');
        }

        $this->db->where('t.branch_id', $branch ? $branch : $this->session->userdata('branch_id'));
        if ($ticket_num) {
            $this->db->where('wt.ticket_num', $ticket_num);
        }
        // its_turn is a flag used to identify ticket is still in the queue
        $this->db->where('t.status = 0 AND t.its_turn = 1');

        $this->db->group_by('t.id');

        $all_waiting = $this->db->get()->result();
        foreach ($all_waiting as $row) {
            $row->final_priority_level = $row->counter + (500 - $row->priority);
            if ($row->reservation_priority) {
                $row->final_priority_level += (500 - $row->reservation_priority);
            }
            $row->calculated_waiting_time = $this->get_datetime_diff($row->entering_time, null);
        }
        // Priority based
        //        if ($calling_technique[2]) {
        //            $keys = array_column($all_waiting, 'final_priority_level');
        //            array_multisort($keys, SORT_ASC, $all_waiting);
        //        }
        // Load Balance
        if ($calling_technique[3]) {
            // Load Balance
            $waiting_list = [];

            // Tickets grouping for each service
            foreach ($all_waiting as $ticket) {
                $waiting_list[$ticket->service_id]['tickets'][] = $ticket;
            }

            // Asign load balance count for each service
            foreach ($waiting_list as $service => $data) {
                // Get terminal load balance count
                $terminal_data = $this->db->get_where('terminal', ['window_no' => $terminal ? $terminal : $this->session->userdata('terminal_window_no'), 'branch_id' => $branch ? $branch : $this->session->userdata('branch_id')])->row();
                if ($terminal_data) {
                    $terminal_service_balance = $this->db->get_where('terminal_service_load_balance', ['terminal_id' => $terminal_data->id, 'service_id' => $service])->row();
                    $count = (!empty($terminal_service_balance) && $terminal_service_balance->count > 0) ? $terminal_service_balance->count : 5;
                    $waiting_list[$service]['count'] = $count;
                } else {
                    $waiting_list[$service]['count'] = 5;
                }
            }
            $total_count = count($all_waiting);
            // Create final waiting list with Load balance count
            $final_tickets = [];
            while (count($final_tickets) < $total_count) {
                foreach ($waiting_list as $service => $data) {
                    $i = 0;
                    foreach ($data['tickets'] as $key => $ticket) {
                        if ($i < $data['count']) {
                            $final_tickets[] = $ticket;
                            unset($waiting_list[$service]['tickets'][$key]);
                            $i++;
                        }
                    }
                }
            }
            return $one ? ($final_tickets ? $final_tickets[0] : []) : $final_tickets;
        }
        return $one ? ($all_waiting ? $all_waiting[0] : []) : $all_waiting;
    }

    public function get_holding_transactions($branch = null, $terminal = null, $limit = 0, $start = 0)
    {
        $this->db->select('distinct(transactions.id),transactions.*');
        $this->db->from('transactions');
        $this->db->join('escalate_manual', 'escalate_manual.transaction_id = transactions.id', 'left');
        $this->db->join('work_transactions', 'work_transactions.id = transactions.work_transaction_id');
        $this->db->join('terminal', 'terminal.id=' . ($terminal ? $terminal : $this->session->userdata('terminal_id_at_table')));
        $this->db->join('terminal_has_service', 'terminal_has_service.service_id = transactions.service_id AND terminal_has_service.terminal_key=terminal.key', 'left');
        $this->db->order_by('escalate_manual.counter, work_transactions.enter_time'); //priority.prioity_level
        $this->db->where('transactions.terminal_id = ' . ($terminal ? $terminal : $this->session->userdata('terminal_window_no')));
        $this->db->where('transactions.branch_id', ($branch ? $branch : $this->session->userdata('branch_id')));
        $this->db->where('( transactions.status = 5)');
        if ($limit):
            $this->db->limit($limit, $start);
        endif;
        return $this->db->get()->result();
    }

    public function get_serving_ticket($branch = null, $terminal = null)
    {
        $this->db->select('transactions.*');
        $this->db->from('transactions');
        $this->db->order_by('entering_time');
        $this->db->where('branch_id', $branch ? $branch : $this->session->userdata('branch_id'));
        $this->db->where('terminal_id', $terminal ? $terminal : $this->session->userdata('terminal_window_no'));
        $this->db->where('status', 1);
        return $this->db->get()->row();
    }

    public function get_ticket_num($id)
    {
        $this->db->select('id,ticket_num,work_transaction_id');
        return $this->db->get_where('transactions', array('id' => $id))->row();
    }

    public function serve_ticket($id, $terminal = null, $agent = null, $agent_key = null)
    {
        $today = date('Y-m-d H:i:s');
        $this->db->where('id', $id);
        $this->db->from('transactions');
        $transaction = $this->db->get()->row();
        if ($transaction) {
            $entering_date_time = new DateTime($transaction->entering_time);
            $current_date_time = new DateTime();
            $interval = $entering_date_time->diff($current_date_time);
            $waiting_time = $interval->format('%H:%I:%S');

            $data = array('status' => 1, 'its_turn' => 0, 'fake_its_turn' => 0,
                'terminal_id' => $terminal ? $terminal : $this->session->userdata('terminal_window_no'),
                'waiting_time' => $waiting_time, 'waiting_date_time' => $today,
                'session_id' => $agent ? null : $this->session->userdata('session_id'),
                'agent_id' => $agent ? $agent : $this->session->userdata('login'),
                'agent_key' => $agent_key ? $agent_key : $this->session->userdata('agent_key'));

            $this->db->where('id', $id);
            $this->db->update('transactions', $data);
            return $transaction->service_id;
        }
        return null;
    }

    public function set_turn_to_next_ticket($work_transaction_id, $order)
    {
        // mark next service as its turn
        $this->db->where(array('work_transaction_id' => $work_transaction_id, 'order' => $order + 1, 'status' => 0));
        $this->db->update('transactions', array('its_turn' => 1, 'fake_its_turn' => 1));
    }

    public function Hold_ticket($id)
    {
        $this->db->where('id', $id);
        $this->db->from('transactions');
        $transaction = $this->db->get()->row();
        if ($transaction) {
            $current_date_time = date('Y-m-d H:i:s');
            if ($transaction->holding_time == null) {
                $serving_to_now = $this->get_datetime_diff($transaction->waiting_date_time, null);
            } else { // if hold for many times
                $serving_last_period = $this->get_datetime_diff($transaction->resuming_time, null);
                $secs = strtotime($serving_last_period) - strtotime("00:00:00");
                $serving_to_now = date('H:i:s', strtotime($transaction->serving_time) + $secs);
            }
            $data = array('status' => 5, 'holding_time' => $current_date_time, 'serving_time' => $serving_to_now);
            $this->db->where('id', $id);
            $this->db->update('transactions', $data);
            return $transaction->ticket_num;
        }
        return '-';
    }

    public function back_to_queue_ticket($id)
    {
        $this->db->where('id', $id);
        $this->db->from('transactions');
        $transaction = $this->db->get()->row();
        if ($transaction) {
            $current_date_time = date('Y-m-d H:i:s');
            $serving_to_now = $this->get_datetime_diff($transaction->waiting_date_time, null);

            $data = array('status' => 0, 'serving_time' => $serving_to_now, 'terminal_id' => 0, 'its_turn' => 1);
            $this->db->where('id', $id);
            $this->db->update('transactions', $data);
            return $transaction->ticket_num;
        }
        return '-';
    }

    public function Resume_ticket($id)
    {
        $this->db->where('id', $id);
        $this->db->from('transactions');
        $transaction = $this->db->get()->row();
        if ($transaction) {
            $current_date_time = date('Y-m-d H:i:s');
            $data = array('status' => 1, 'resuming_time' => $current_date_time);
            $this->db->where('id', $id);
            $this->db->update('transactions', $data);
        }
    }

    public function close_current_ticket($id)
    {
        $this->db->where('id', $id);
        $this->db->from('transactions');
        $transaction = $this->db->get()->row();
        if ($transaction) {
            if ($transaction->resuming_time != null) {
                $resuming_date_time = new DateTime($transaction->resuming_time);
                $serving_date_time = new DateTime($transaction->serving_time);
                $current_date_time = new DateTime();
                $interval = $resuming_date_time->diff($current_date_time);
                $serving_date_time->add($interval);
                $interval2 = $serving_date_time->format('H:i:s');
                $serving_time = $interval2;
                $data = array('status' => 2, 'serving_time' => $serving_time, 'end_serving' => date('Y-m-d H:i:s'));
            } else {
                $waiting_date_time = new DateTime($transaction->waiting_date_time);
                $serving_date_time = new DateTime($transaction->serving_time);
                $current_date_time = new DateTime();
                $interval = $waiting_date_time->diff($current_date_time);
                $serving_date_time->add($interval);
                $interval2 = $serving_date_time->format('H:i:s');
                $serving_time = $interval2;
                $data = array('status' => 2, 'serving_time' => $serving_time, 'end_serving' => date('Y-m-d H:i:s'));
            }
            $this->db->where('id', $id);
            $this->db->update('transactions', $data);
            // check for same work_transaction_id if all is closed and send survey
            $this->db->where('work_transaction_id', $transaction->work_transaction_id);
            $this->db->where('status !=', 2);
            $this->db->from('transactions');
            $pending_work_trans = $this->db->get()->row();
            if ($pending_work_trans == 0) {
                // work flow finished. Get customer id and send random survey
                $this->db->where('id', $transaction->work_transaction_id);
                $this->db->from('work_transactions');
                $work_transaction = $this->db->get()->row();
                if ($work_transaction) {
                    if ($work_transaction->reservation_id != null) {
                        $this->db->select('customers.*');
                        $this->db->from('customers');
                        $this->db->join('reservations', 'reservations.customer_id = customers.id');
                        $this->db->where('reservations.id', $work_transaction->reservation_id);
                        $customer_data = $this->db->get()->row();
                        // get random survey and email customer
                        $this->db->where('active', 'Y');
                        $this->db->order_by('rand()');
                        $this->db->limit(1);
                        $survey = $this->db->get('wam_surveys')->row();
                        if ($survey) {
                            $survey_url = base_url() . 'wam_feedback/index.php/' . $survey->sid . '/?lang=' . $survey->language;
                            $this->email_survey_to_customer($survey_url, $customer_data->email);
                        }
                    }
                }
            }
            // ======== set enter time for next service correct ============
            // to be served if clicked next
            $next_service_in_ticket = $this->db->get_where('transactions', array('work_transaction_id' => $transaction->work_transaction_id, 'order' => $transaction->order + 1, 'status' => 0))->row();
            // to be waiting

            if ($next_service_in_ticket) {
                $this->db->update('transactions', array('entering_time' => date('Y-m-d H:i:s'), 'its_turn' => 1, 'fake_its_turn' => 1), array('id' => $next_service_in_ticket->id));
            }
            return $transaction->ticket_num;
        }
        return '-';
    }

    public function convert_time_to_sec($time)
    {
        $time_arr = explode(":", $time);
        $time_in_sec = (int) $time_arr[0] * 60 * 60 + (int) $time_arr[1] * 60 + (int) $time_arr[2];
        return $time_in_sec;
    }

    public function format_time($time_in_sec)
    {
        if ($time_in_sec > 3600) {
            $hours = intval($time_in_sec / 3600);
            if (strlen((string) $hours) < 2) {
                $hours = '0' . $hours;
            }
            $min = intval(($time_in_sec % 3600) / 60);
            if (strlen((string) $min) < 2) {
                $min = '0' . $min;
            }
            $sec = ($time_in_sec % 3600) % 60;
            if (strlen((string) $sec) < 2) {
                $sec = '0' . $sec;
            }
            return $hours . ':' . $min . ':' . $sec;
        } elseif ($time_in_sec > 60) {
            $min = intval($time_in_sec / 60);
            if (strlen((string) $min) < 2) {
                $min = '0' . $min;
            }
            $sec = $time_in_sec % 60;
            if (strlen((string) $sec) < 2) {
                $sec = '0' . $sec;
            }
            return '00:' . $min . ':' . $sec;
        } else {
            $sec = intval($time_in_sec);
            if (strlen((string) $sec) < 2) {
                $sec = '0' . $sec;
            }
            return '00:00:' . $sec;
        }
    }

    public function get_branch_terminals_except_mine($branch_id)
    {
        $this->db->select('id,window_no');
        $this->db->from('terminal');
        $this->db->where('branch_id', $branch_id);
        $this->db->where('window_no !=', $this->session->userdata('terminal_window_no'));
        $this->db->where('status', 1);
        $this->db->where('idle_state', 1);
        return $this->db->get()->result();
    }

    public function get_specific_branch_terminal($terminal_id)
    {
        $this->db->select('terminal.*');
        $this->db->from('terminal');
        $this->db->where('branch_id', $this->session->userdata('branch_id'));
        $this->db->where('window_no', $terminal_id);
        return $this->db->get()->row();
    }

    public function get_terminals_order_num($branch, $order_num)
    {
        $this->db->select('terminal.*');
        $this->db->from('terminal');
        $this->db->where('branch_id', $branch);
        $this->db->where('order_num', $order_num);
        return $this->db->get()->row();
    }

    public function get_workstation_settings($branch_id)
    {
        return $this->db->get_where('workstation_layout_sett', array('branch_id' => $branch_id))->row();
    }

    public function get_currently_serving($terminal_win_no)
    {
        $this->db->select('ticket_num');
        $serving = $this->db->get_where('transactions', array('terminal_id' => $terminal_win_no,
            'transactions.branch_id' => $this->session->userdata('branch_id'),
            'transactions.status' => 1))->row();
        $terminal_idle = $this->db->get_where('terminal', array('window_no' => $terminal_win_no,
            'branch_id' => $this->session->userdata('branch_id'),
            'idle_state' => 1))->num_rows();
        return array($serving, $terminal_idle);
    }

    public function get_currently_serving_customer($terminal_win_no, $branch)
    {
        $this->db->select('ticket_num');
        $serving = $this->db->get_where('transactions', array('terminal_id' => $terminal_win_no,
            'transactions.branch_id' => $branch,
            'transactions.status' => 1))->row();
        $terminal_idle = $this->db->get_where('terminal', array('window_no' => $terminal_win_no,
            'branch_id' => $branch,
            'idle_state' => 1))->num_rows();
        return array($serving, $terminal_idle);
    }

    public function get_waiting_area_settings($w_area_id)
    {
        return $this->db->get_where('waiting_area_layout_sett', array('waiting_area_id' => $w_area_id))->row();
    }

    public function get_windows_with_serving($waiting_area_id, $branch_id, $sequence_of_display, $terminals_per_page, $start_terminal = 0)
    {
        $this->db->select('terminal.id');
        $this->db->join('terminal', 'terminal.key=waiting_area_terminals.terminal_key');
        $assigned_terminals = $this->db->get_where('waiting_area_terminals', array('waiting_area_id' => $waiting_area_id))->result();
        $terminal_id_arr = array();
        foreach ($assigned_terminals as $terminal) {
            array_push($terminal_id_arr, $terminal->id);
        }
        if (empty($terminal_id_arr)) {
            return array();
        }
        $this->db->select('t.window_no as terminal_id,tran.ticket_num');
        $this->db->limit($terminals_per_page, $start_terminal);
        if ($sequence_of_display == 0) {
            $this->db->order_by('t.order_num');
        } else {
            $this->db->order_by('tran.waiting_date_time', 'DESC');
        }
        $this->db->where_in('t.id', $terminal_id_arr);
        $this->db->join('transactions tran', 't.window_no = tran.terminal_id and tran.status = 1 and tran.branch_id = ' . $branch_id, 'left');
        return $this->db->get_where('terminal t', array('t.branch_id' => $branch_id, 't.status' => 1))->result();
    }

    public function set_next_ticket_turn()
    {
        $this->db->select('transactions.*');
        $this->db->from('transactions');
        $this->db->where('branch_id', $this->session->userdata('branch_id'));
        $this->db->where('terminal_id', $this->session->userdata('terminal_window_no'));
        $this->db->where('its_turn', 1);
        $first_ticket = $this->db->get()->row();

        $next_service_in_ticket = $this->db->get_where('transactions', array('work_transaction_id' => $first_ticket->work_transaction_id, 'order' => $first_ticket->order + 1, 'status' => 0))->row();

        if ($next_service_in_ticket) {
            $this->db->update('transactions', array('its_turn' => 1, 'fake_its_turn' => 1), array('id' => $next_service_in_ticket->id));
        }
    }

    public function get_datetime_diff($datetime1, $datetime2 = null)
    {
        $datetime1 = new DateTime($datetime1);
        if ($datetime2 == null) {
            $datetime2 = new DateTime(date('Y-m-d H:i:s'));
        } else {
            $datetime2 = new DateTime($datetime2);
        }
        $diff = $datetime2->diff($datetime1);
        $formatted_diff = '';
        if ($diff->y != 0) {
            $formatted_diff .= $diff->y . ' Year ';
        }
        if ($diff->m != 0) {
            $formatted_diff .= $diff->m . ' Month ';
        }
        if ($diff->d != 0) {
            $formatted_diff .= $diff->d . ' day ';
        }
        $formatted_diff .= $diff->format('%H:%I:%S');
        return $formatted_diff;
    }

    public function get_agent_services($agent_key = null)
    {
        $this->db->select('s.id,s.service_name,a_s.assigned_timestamp,a_s.expire_period,s.can_choose_agent');
        $this->db->join('service s', 's.id=a_s.service_id AND s.deleted=0');
        $all_services = $this->db->get_where('agent_services a_s', array('a_s.agent_key' => $agent_key ? $agent_key : $this->session->userdata('agent_key')))->result();
        $i = 0;
        foreach ($all_services as $service) {
            if ($service->expire_period != null) {
                $service_expire = strtotime($service->assigned_timestamp . ' + ' . $service->expire_period . ' minute');
                if (date('Y-m-d H:i:s') > date('Y-m-d H:i:s', $service_expire)) {
                    unset($all_services[$i]);
                }
            }
            $i++;
        }
        return $all_services;
    }

    public function get_slideshow_imgs($branch_id)
    {
        $this->db->select('s_p.photo');
        $this->db->join('slideshow_photos s_p', 's_p.work_waiting_id=w_s.id AND s_p.type=0');
        return $this->db->get_where('counter_layout_sett w_s', array('w_s.branch_id' => $branch_id))->result();
    }

    public function get_waiting_areas_slideshow_imgs($area_id)
    {
        $this->db->select('s_p.photo'); // area_id  id in table waiting_areas
        return $this->db->get_where('slideshow_photos s_p', array('s_p.work_waiting_id' => $area_id, 'type' => 1))->result();
    }

    public function get_branch_news($branch_id)
    {
        $this->db->select('news.*');
        $this->db->where('for_all_branches', 1);
        $this->db->or_where('news_branches.branch_id', $branch_id);
        $this->db->join('news_branches', 'news_branches.news_id=news.id', 'left');
        return $this->db->get('news')->result();
    }

    public function get_email_settings()
    {
        $query = $this->db->get('email_settings');
        return $query->row();
    }

    public function email_survey_to_customer($survey_url, $customer_email)
    {
        $msg_content = '';
        $email_settings = $this->get_email_settings();
        $msg_content .= "Dear Customer <br>";
        $msg_content .= 'Please answer this survey. <a href="' . $survey_url . '" title="WAM Survey"> WAM Survey </a>';
        // config email smtp
        $config = array(
            'protocol' => 'smtp',
            'smtp_host' => $email_settings->smtp_host, //ssl://smtp.googlemail.com
            'smtp_port' => $email_settings->smtp_port, // 465
            'smtp_user' => $email_settings->smtp_user,
            'smtp_pass' => $email_settings->smtp_pass,
            'mailtype' => 'html',
            'charset' => 'iso-8859-1',
        );
        $this->load->library('email');
        $this->email->initialize($config);
        $this->email->from($email_settings->host_mail);
        $this->email->to($customer_email);
        $this->email->subject('WAM Survey');
        $this->email->message($msg_content);
        $this->email->send();
    }

    public function get_terminals($branch)
    {
        return $this->db->select('*')->from('terminal')->where('branch_id', $branch)->get()->result();
    }

    public function load_balance_service($terminal_id)
    {
        $this->db->select('service.*, terminal_service_load_balance.terminal_id, terminal_service_load_balance.count');
        $this->db->from('service');
        $this->db->join('terminal_service_load_balance', 'terminal_service_load_balance.service_id = service.id', 'left');
        $this->db->where('terminal_service_load_balance.terminal_id', $terminal_id);
        return $this->db->get()->result();
    }

    public function get_branch_counters($branch_id, $id = null)
    {
        return $this->db->get_where('counter', ['branch_id' => $branch_id, 'id !=' => $id])->result();
    }

    public function get_workstation_layout_sett($branch_id, $waiting_area_id)
    {
        return $this->db->get_where('counter_layout_sett', array('branch_id' => $branch_id, 'waiting_area_id' => $waiting_area_id))->row();
    }

    public function get_branch_workstations($branch_id, $id = null)
    {
        return $this->db->get_where('counter', ['branch_id' => $branch_id])->result();
    }

    public function get_workstation_slideshow_imgs($area_id)
    {
        $this->db->select('s_p.photo'); // area_id  id in table waiting_areas
        return $this->db->get_where('slideshow_photos s_p', array('s_p.work_waiting_id' => $area_id, 'type' => 0))->result();
    }

    public function get_workstation_ticket($branch_id, $number)
    {
        return $this->db->get_where('transactions', ['branch_id' => $branch_id, 'terminal_id' => $number, 'status' => 1])->row();
    }

    public function get_waiting_areas_tickets_bar($branch_id, $number, $srvs = [])
    {
        $this->db->select('*');
        $this->db->from('transactions');
        $this->db->where('branch_id', $branch_id);
        $this->db->where('terminal_id', $number);
        $this->db->where('status', 1);
        if (!empty($srvs)) {
            $this->db->where_in('service_id', $srvs);
        }
        return $this->db->get()->result();
    }

    public function get_waiting_areas_tickets_tbl($branch_id, $number, $srvs = [])
    {
        $this->db->select('*');
        $this->db->from('transactions');
        $this->db->where('branch_id', $branch_id);
        $this->db->where('terminal_id', $number);
        $this->db->where('status', 1);
        if (!empty($srvs)) {
            $this->db->where_in('service_id', $srvs);
        }
        return $this->db->get()->result();
    }

    public function get_currencies($limit, $start)
    {
        $this->db->select('*');
        $this->db->from('currencies');
        $this->db->limit($limit, $start);
        return $this->db->get()->result();
    }

    public function get_workstations_terminals($branch_id, $id = 0)
    {
        $this->db->select('number');
        $this->db->from('counter');
        if ($id) {
            $this->db->where('id !=', $id);
        }
        $this->db->where('branch_id', $branch_id);
        return $this->db->get()->result();
    }
}
