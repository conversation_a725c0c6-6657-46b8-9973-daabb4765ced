<?php

defined('BASEPATH') or exit('No direct script access allowed');

class C_terminal extends CI_check_login
{
    public function __construct()
    {
        $this->require_login = false;
        parent::__construct();
        $this->load->model('M_terminal');
        $this->load->model('M_settings');
        $this->load->model('M_branch');
        $this->load->model('Crud_model');
        $this->load->model('M_templates');
        $this->load->library('config_writer');
        $this->lang->load('eng');
        $this->lang->load('eng_2');
    }

    public function index($branch_id)
    {
        parent::login_required();
        if ($this->session->userdata('login')) {
            $terminals = $this->M_terminal->my_get_all_data($branch_id);
            if (isset($this->session->userdata("license")['number_of_supported_workstations']) && $this->session->userdata("license")['number_of_supported_workstations']['count'] > count($terminals)) {
                if (get_p("terminal_lvl", "c")) {
                    $this->load->model('M_general');
                    $user_brnches = $this->M_general->get_user_branches();
                    if (! in_array($branch_id, $user_brnches)) {
                        $error['text'] = $this->lang->line('permission_to_branch');
                        $this->load->view('private/page-500', $error);
                        return;
                    }
                    $this->load->model('m_users');
                    $arr['branches']        = $this->m_users->get_user_branches();
                    $arr['internal_areas']  = $this->M_terminal->get_internal_area_all_data($branch_id);
                    $arr['active']          = 'terminal';
                    $arr['selected_branch'] = $branch_id;
                    $branch_exist           = $this->M_terminal->get_branch_name($branch_id);
                    if (! $branch_exist) {
                        $error['text'] = 'Branch not found';
                        $this->load->view('private/page-500', $error);
                        return;
                    }
                    $arr['branch_name'] = $branch_exist->EnglishBranchName;
                    CI_load_view::load_view('branch/terminal_form', $arr);
                } else {
                    $error['text'] = $this->lang->line('permission_denied');
                    $this->load->view('private/page-500', $error);
                }
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function load_edit_form($id)
    {
        parent::login_required();
        if ($this->session->userdata('login')) {
            if (get_p("terminal_lvl", "u")) {
                $arr                           = $this->M_terminal->get_data_with_id($id);
                $arr['data']                   = $arr['row'];
                $arr['data']->comLayer         = ! empty($arr['row']->comLayer) ? unserialize($arr['row']->comLayer) : [];
                $arr['data']->mqtt_ch          = ! empty($arr['row']->mqtt_ch) ? $arr['row']->mqtt_ch : '';
                $arr['data']->mqtt_terminal_id = ! empty($arr['row']->mqtt_terminal_id) ? $arr['row']->mqtt_terminal_id : '';
                $arr['data']->mqtt             = ! empty($arr['row']->mqtt) ? unserialize($arr['row']->mqtt) : [];
                $arr['data']->poe              = ! empty($arr['row']->poe) ? unserialize($arr['row']->poe) : [];
                $arr['data']->audio_controller = ! empty($arr['row']->audio_controller) ? unserialize($arr['row']->audio_controller) : [];
                $arr['ips']                    = $arr['ip_digits'];
                $arr['selected_branch']        = $arr['row']->branch_id;
                $this->load->model('M_general');
                $user_brnches = $this->M_general->get_user_branches();
                if (! in_array($arr['selected_branch'], $user_brnches)) {
                    $error['text'] = $this->lang->line('permission_to_branch');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                $branch_exist = $this->M_terminal->get_branch_name($arr['row']->branch_id);
                if (! $branch_exist) {
                    $error['text'] = 'Branch not found';
                    $this->load->view('private/page-500', $error);
                    return;
                }
                $arr['branch_name'] = $branch_exist->EnglishBranchName;
                $this->load->model('m_users');
                $arr['branches']       = $this->m_users->get_user_branches();
                $arr['internal_areas'] = $this->M_terminal->get_internal_area_all_data($arr['row']->branch_id);
                $arr['active']         = 'terminal';
                CI_load_view::load_view('branch/terminal_form', $arr);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function check($edit, $id)
    {
        parent::login_required();
        if ($this->session->userdata('login')) {
            $this->form_validation->set_rules('win_no', $this->lang->line('win_no'), 'required|integer');
            $this->form_validation->set_rules('display_address', $this->lang->line('display_add'), 'required|integer');
            $this->form_validation->set_rules('BranchIntAreaID', $this->lang->line('internal_area'), 'required|integer');
            $this->form_validation->set_rules('floor', $this->lang->line('floor'), 'integer|greater_than_equal_to[0]|less_than_equal_to[10]');
            $this->form_validation->set_rules('calling[]', $this->lang->line('calling_mechanism'), 'required');
            if ($this->form_validation->run() == false) {
                echo validation_errors();
            } else {
                if ($edit == 0) {
                    $repeated = $this->M_terminal->repeated_win_or_display($this->input->post('branch'), $this->input->post('win_no'), $this->input->post('display_address'), $this->input->post('BranchIntAreaID'));
                } else {
                    $repeated = $this->M_terminal->repeated_win_or_display_edit($this->input->post('branch'), $this->input->post('win_no'), $this->input->post('display_address'), $this->input->post('BranchIntAreaID'), $id);
                }
                if (! $repeated) {
                    echo 'yes';
                } else {
                    echo $repeated;
                }
            }
        }
    }

    public function is_valid_numbers()
    {
        parent::login_required();
        if ($this->input->post('order_num') > 0) {
            return true;
        } else {
            $this->form_validation->set_message('is_valid_numbers', $this->lang->line('check_order_num'));
            return false;
        }
    }

    public function save_data()
    {
        parent::login_required();
        if ($this->session->userdata('login')) {
            if (get_p("terminal_lvl", "c")) {
                $default_calling = null;
                $e_calling       = [];
                $call_arr        = $this->input->post('calling[]');
                foreach ($call_arr as $k => $v):
                    if ($k == 0) {
                        $default_calling = $v;
                    } else {
                        $e_calling[] = $v;
                    }
                endforeach;
                $win_no               = $this->input->post('win_no');
                $dis_add              = $this->input->post('display_address');
                $dis_active           = $this->input->post('dis_active') == 0 ? 0 : 1;
                $dis_comm             = $this->input->post('dis_comm') == 0 ? 0 : 1;
                $term_comm            = $this->input->post('term_comm') == 0 ? 0 : 1;
                $dir                  = $this->input->post('dir');
                $floor                = $this->input->post('floor');
                $branch_internal_area = $this->input->post('BranchIntAreaID');
                $branch_id            = $this->input->post('branch');
                $calling              = $default_calling;
                $extra_calling        = $e_calling ? implode(',', $e_calling) : null;
                $comLayer             = $this->input->post('comLayer');
                $mqtt_ch              = $this->input->post('mqtt_ch');
                $mqtt_terminal_id     = $this->input->post('mqtt_terminal_id');
                $mqtt                 = $this->input->post('mqtt');
                $poe                  = $this->input->post('poe');
                $audio_controller     = $this->input->post('audio_controller');
                $this->M_terminal->insert_data($win_no, $dis_add, $dis_active, $dis_comm, $term_comm, $dir, $floor, $calling, $comLayer, $mqtt_ch, $mqtt_terminal_id, $mqtt, $poe, $audio_controller, $branch_internal_area, $branch_id, $extra_calling);
                record_log('Add new terminal with window no. : ' . $this->input->post('win_no'));
                update_branch_sett('terminals', $branch_id);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function edit_data($id)
    {
        parent::login_required();
        if ($this->session->userdata('login')) {
            if (get_p("terminal_lvl", "u")) {
                $default_calling = null;
                $e_calling       = [];
                $call_arr        = $this->input->post('calling[]');
                foreach ($call_arr as $k => $v):
                    if ($k == 0) {
                        $default_calling = $v;
                    } else {
                        $e_calling[] = $v;
                    }
                endforeach;
                $win_no               = $this->input->post('win_no');
                $dis_add              = $this->input->post('display_address');
                $dis_active           = $this->input->post('dis_active');
                $dis_comm             = $this->input->post('dis_comm');
                $term_comm            = $this->input->post('term_comm');
                $dir                  = $this->input->post('dir');
                $branch_internal_area = $this->input->post('BranchIntAreaID');
                $branch_id            = $this->input->post('branch');
                $floor                = $this->input->post('floor');
                $calling              = $default_calling;
                $extra_calling        = $e_calling ? implode(',', $e_calling) : null;
                $comLayer             = $this->input->post('comLayer');
                $mqtt_ch              = $this->input->post('mqtt_ch');
                $mqtt_terminal_id     = $this->input->post('mqtt_terminal_id');
                $mqtt                 = $this->input->post('mqtt');
                $poe                  = $this->input->post('poe');
                $audio_controller     = $this->input->post('audio_controller');
                $this->M_terminal->edit_data($win_no, $dis_add, $dis_active, $dis_comm, $term_comm, $dir, $floor, $calling, $extra_calling, $comLayer, $mqtt_ch, $mqtt_terminal_id, $mqtt, $poe, $audio_controller, $branch_internal_area, $branch_id, $id);
                record_log('update terminal with window no. : ' . $this->input->post('win_no'));
                update_branch_sett('terminals', $branch_id);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
            // }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function loadInternalAreas($branch, $success = null)
    {
        parent::login_required();
        if ($this->session->userdata('login')) {
            if (get_p("internal_area_lvl", "v")) {
                $this->load->model('M_general');
                $user_brnches = $this->M_general->get_user_branches();
                if (! in_array($branch, $user_brnches)) {
                    $error['text'] = $this->lang->line('permission_to_branch');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                $arr['data']            = $this->M_terminal->get_internal_area_all_data($branch);
                $arr['active']          = 'internal_areas';
                $arr['user_branches']   = $this->M_terminal->get_branches_details($branch);
                $arr['selected_branch'] = $branch;
                //////////////////// get branch name /////////////////////////////////
                $branch_exist = $this->M_terminal->get_branch_name($branch);
                if (! $branch_exist) {
                    $error['text'] = 'Branch not found';
                    $this->load->view('private/page-500', $error);
                    return;
                }
                $arr['branch_name'] = $branch_exist->EnglishBranchName;
                $arr['success']     = $success;
                CI_load_view::load_view('branch/internal_area_show', $arr);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function add_new_internal_area()
    {
        if ($this->session->userdata('login')) {
            if (check_is_server() && get_p("internal_area_lvl", "c")) {
                $arr['branch_id'] = $this->session->userdata('branch_id');
                $arr['active']    = 'internal_areas';
                CI_load_view::load_view('branch/internal_area_form', $arr);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function load_internal_area_edit_form($id)
    {
        if ($this->session->userdata('login')) {
            if (check_is_server() && get_p("internal_area_lvl", "u")) {
                $arr['branch_id'] = $this->session->userdata('branch_id');
                $arr['data']      = $this->M_terminal->get_internal_area_data_with_id($id);
                $arr['active']    = 'internal_areas';
                CI_load_view::load_view('branch/internal_area_form', $arr);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function check_internal_area($id = null)
    {
        if ($this->session->userdata('login')) {
            $info    = $id ? $this->db->get_where('branch_internal_area', ['AreaID' => $id])->row() : null;
            $success = true;
            if (empty($info) || ($info->EnglishAreaName != $this->input->post('eng_name'))) {
                $success = false;
                $this->form_validation->set_rules('eng_name', $this->lang->line('eng_area_name'), 'trim|required|callback_english_name');
            }
            if (empty($info) || ($info->ArabicAreaName != $this->input->post('ar_name'))) {
                $success = false;
                $this->form_validation->set_rules('ar_name', $this->lang->line('ar_area_name'), 'trim|required|callback_arabic_name');
            }
            if ($this->form_validation->run() == false && (! $info || ($info && ! $success))) {
                echo json_encode([validation_errors(), $this->form_validation->error_array()]);
            } else {
                echo json_encode(['yes']);
            }
        }
    }

    public function english_name()
    {
        $name      = $this->input->post('eng_name');
        $branch_id = $this->session->userdata('branch_id');
        $num       = $this->db->get_where('branch_internal_area', ['EnglishAreaName' => $name, 'BranchID' => $branch_id])->num_rows();
        if ($num == 0) {
            return true;
        }
        $this->form_validation->set_message('english_name', 'The {field} field must be unique');
        return false;
    }

    public function arabic_name()
    {
        $name      = $this->input->post('ar_name');
        $branch_id = $this->session->userdata('branch_id');
        $num       = $this->db->get_where('branch_internal_area', ['ArabicAreaName' => $name, 'BranchID' => $branch_id])->num_rows();
        if ($num == 0) {
            return true;
        }
        $this->form_validation->set_message('arabic_name', 'The {field} field must be unique');
        return false;
    }

    public function save_internal_area_data()
    {
        if ($this->session->userdata('login')) {
            if (check_is_server() && get_p("internal_area_lvl", "c")) {
                $en_name = $this->input->post('eng_name');
                $ar_name = $this->input->post('ar_name');
                $this->M_terminal->insert_internal_area_data($en_name, $ar_name);
                record_log('Add new internal area : ' . $en_name);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function edit_internal_area_data($id)
    {
        if ($this->session->userdata('login')) {
            if (check_is_server() && get_p("internal_area_lvl", "u")) {
                $en_name = $this->input->post('eng_name');
                $ar_name = $this->input->post('ar_name');
                $this->M_terminal->edit_internal_area_data($en_name, $ar_name, $id);
                record_log('update data of internal area : ' . $en_name);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function delete_internal_area($id)
    {
        if ($this->session->userdata('login')) {
            if (check_is_server() && get_p("internal_area_lvl", "d")) {
                $row = $this->db->get_where('branch_internal_area', ['AreaID' => $id])->row();
                $this->M_terminal->delete_internal_area_record($id);
                if ($row) {
                    record_log('Delete internal area : ' . $row->EnglishAreaName);
                }
                redirect(base_url() . 'C_terminal/loadInternalAreas/' . $this->session->userdata('branch_id') . "/success");
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function loadShow($branch, $success = null)
    {
        parent::login_required();
        if ($this->session->userdata('login')) {
            if (get_p("terminal_lvl", "v")) {
                $this->load->model('M_general');
                $user_brnches = $this->M_general->get_user_branches();
                if (! in_array($branch, $user_brnches)) {
                    $error['text'] = $this->lang->line('permission_to_branch');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                $arr['data']            = $this->M_terminal->my_get_all_data($branch);
                $arr['active']          = 'terminal';
                $arr['user_branches']   = $this->M_terminal->get_branches_details($branch);
                $arr['selected_branch'] = $branch;
                //////////////////// get branch name /////////////////////////////////
                $branch_exist = $this->M_terminal->get_branch_name($branch);
                if (! $branch_exist) {
                    $error['text'] = 'Branch not found';
                    $this->load->view('private/page-500', $error);
                    return;
                }
                $arr['branch_name'] = $branch_exist->EnglishBranchName;
                $arr['success']     = $success;
                CI_load_view::load_view('branch/terminal_show', $arr);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function delete($id)
    {
        parent::login_required();
        if ($this->session->userdata('login')) {
            if (get_p("terminal_lvl", "d")) {
                $branch = $this->M_terminal->get_branch_of_terminal($id);
                if (! $branch) {
                    $error['text'] = $this->lang->line('terminal_not_found');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                $branch_id = $branch->branch_id;
                $row       = $this->db->get_where('terminal', ['id' => $id])->row();
                $this->M_terminal->delete_record($id);
                if ($row) {
                    record_log('Delete terminal with window no. : ' . $row->window_no);
                }
                update_branch_sett('terminals', $branch_id);
                redirect(base_url() . 'C_terminal/loadShow/' . $branch_id);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    // if no user currently assign for this terminal then terminal_has_user_id = 0.
    public function load_assign_agent($terminal_has_user_id, $current_agent_user_name, $terminal_id)
    {
        parent::login_required();
        if ($this->session->userdata('login') && get_p("assign_agent_terminal_lvl", "v")) {
            if ($terminal_has_user_id == 0) { //No Agent assigned for this terminal
                $arr['agents']               = $this->M_terminal->get_available_agents($terminal_id);
                $arr['terminal_has_user_id'] = 0;
            } else {
                $arr['terminal_has_user_id'] = $terminal_has_user_id;
                $arr['current_username']     = $current_agent_user_name;
            }
            $arr['terminal_id'] = $terminal_id;
            $arr['active']      = 'terminal';
            CI_load_view::load_view('branch/assign_agent_to_terminal', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function unassin_and_get_available_agents($terminal_has_user_id, $terminal_id)
    {
        parent::login_required();
        if ($this->session->userdata('login') && get_p("assign_agent_terminal_lvl", "u")) {
            $this->M_terminal->unassign_agent($terminal_has_user_id);
            $agents   = $this->M_terminal->get_available_agents($terminal_id);
            $terminal = $this->db->get_where('terminal', ['id' => $terminal_id])->row();
            record_log('unassign agent from terminal window no. : ' . $terminal ? $terminal->window_no : '');
            foreach ($agents as $user) {
                echo '<tr>';
                echo '<td>' . $user->username . '</td>';
                echo '<td><a href="' . site_url() . '/C_terminal/assign_user_to_terminal/' . $terminal_id . '/' . $user->id . '"">Assign</a></td>';
                echo '</tr>';
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function assign_user_to_terminal($terminal_id, $user_id)
    {
        parent::login_required();
        if ($this->session->userdata('login') && get_p("assign_agent_terminal_lvl", "u")) {
            $this->M_terminal->assign_agent($terminal_id, $user_id);
            $user       = $this->db->get_where('Users', ['UserID' => $user_id])->row();
            $terminal   = $this->db->get_where('terminal', ['id' => $terminal_id])->row();
            $username_e = $user ? $user->UserName : '';
            $terminal_e = $terminal ? $terminal->window_no : '';
            record_log('assign agent : ' . $username_e . ' to terminal window no. ' . $terminal_e);
            redirect(site_url() . '/C_terminal/loadShow');
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function update_terminal_status()
    {
        parent::login_required();
        if ($this->session->userdata('login')) {
            if (get_p("terminal_lvl", "u")) {
                $post_data = $this->input->post();
                $branch    = $this->M_terminal->get_branch_of_terminal($post_data['terminal_id']);
                if (! $branch) {
                    $error['text'] = $this->lang->line('terminal_not_found');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                $branch_id = $branch->branch_id;
                $this->M_terminal->update_terminal_status($post_data['terminal_id'], $post_data['status']);
                $terminal    = $this->db->get_where('terminal', ['id' => $post_data['terminal_id']])->row();
                $ter_win_num = $terminal ? $terminal->window_no : '';
                $ter_status  = $post_data['status'] ? 'ON' : 'OFF';
                record_log('update status of terminal window no. : ' . $ter_win_num . ' to ' . $ter_status);
                update_branch_sett('terminals', $branch_id);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function get_terminals()
    {
        parent::login_required();
        if ($this->session->userdata('login')) {
            $branch_id = $this->input->post('id');
            $terminals = $this->M_terminal->get_branch_terminals($branch_id);
            echo json_encode($terminals);
        }
    }

    public function get_empty_terminals()
    {
        parent::login_required();
        if ($this->session->userdata('login')) {
            $branch_id = $this->input->post('id');
            $terminals = $this->M_terminal->get_branch_empty_terminals($branch_id);
            echo json_encode($terminals);
        }
    }

    public function get_branches()
    {
        if ($this->session->userdata('login')) {
            $area_id  = $this->input->post('id');
            $branches = $this->M_terminal->get_branches_2($area_id);
            echo json_encode($branches);
        }
    }

    public function set_terminal()
    {
        parent::login_required();
        if ($this->session->userdata('login')) {
            $terminal_id = $this->input->post('terminal_id');
            $login_id    = $this->session->userdata('loginID');
            $branch_id   = $this->session->userdata('User_type') == "User" ? $this->input->post('branch_id') : $this->session->userdata('branch_id');
            if ($branch_id == null) {
                $this->session->set_userdata('branch_id', 0);
            } else {
                $this->session->set_userdata('branch_id', $branch_id);
            }
            if ($this->input->post('Areas') != '') {
                $this->session->set_userdata('area', $this->input->post('Areas'));
            }
            $terminal_data = $this->M_terminal->get_terminal_id($terminal_id, $branch_id);
            $ID_at_table   = $terminal_id != 0 ? $terminal_data->id : '';
            $this->session->set_userdata('terminal_id_at_table', $ID_at_table);
            $this->session->set_userdata('terminal_window_no', $terminal_id);
            $set_terminal = [
                'branchID'     => $branch_id,
                'window_no'    => $terminal_id != 0 ? $terminal_id : '',
                'terminalID'   => $ID_at_table != '' ? $ID_at_table : '',
                'terminal_key' => $terminal_data ? $terminal_data->key : '',
            ];
            $this->Crud_model->update('sessions', $login_id, $set_terminal);
            record_log('User logged in : ' . $this->session->userdata('username'));
            $this->M_terminal->update_terminal_status2($set_terminal['terminalID'], 1);
            echo json_encode(true);
        }
    }

    public function serve_ticket($id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("serve_waiting_lvl", "v")) {
                $serving_ticket = $this->M_terminal->get_serving_ticket();
                if ($serving_ticket) {
                    $this->M_terminal->close_current_ticket($serving_ticket->id);
                }
                $service_id       = null;
                $writer           = $this->config_writer->get_instance();
                $terminal_id      = "current_serve_ticket_" . $this->session->userdata('terminal_id_at_table');
                $t_num            = '---';
                $branch_terminal  = $this->M_terminal->get_branch_terminal();
                $comLayer         = $branch_terminal ? unserialize($branch_terminal->comLayer) : null;
                $mqtt             = $branch_terminal ? unserialize($branch_terminal->mqtt) : null;
                $poe              = $branch_terminal ? unserialize($branch_terminal->poe) : null;
                $audio_controller = $branch_terminal ? unserialize($branch_terminal->audio_controller) : null;
                if ($id == 0) {
                    $transaction_id   = '';
                    $services_num_arr = [];
                    if ($branch_terminal) {
                        $agent_key     = $this->session->userdata('agent_key');
                        $service_array = defined(constant_name: TERMINAL_SERVICE_TYPE) && TERMINAL_SERVICE_TYPE == 2 ? $this->M_terminal->get_agent_services($agent_key) : $this->M_terminal->get_terminal_has_services($branch_terminal->id);
                        $choose_agent       = false;
                        foreach ($service_array as $service) {
                            if (! in_array($service->id, $services_num_arr)) {
                                if ($service->can_choose_agent) {
                                    $choose_agent = true;
                                }
                                array_push($services_num_arr, $service->id);
                            }
                        }
                        // get from transactions
                        $calling_mechanism   = terminal_calling_technique($branch_terminal);
                        $waiting_transaction = $this->M_terminal->get_waiting_transactions($services_num_arr, $calling_mechanism, true, null, null, null, null, $choose_agent);
                        if ($waiting_transaction) {
                            //$this->send_notification_before_turn_with_x($services_num_arr, $calling_mechanism);
                            $service_id    = $this->M_terminal->serve_ticket($waiting_transaction->id);
                            $service_audio = false;
                            $service_info  = $this->db->select('sound_muted')->get_where('service', ['id' => $service_id])->row();
                            if ($service_info && ! $service_info->sound_muted) {
                                $service_audio = true;
                            }
                            $t_num          = $waiting_transaction ? $waiting_transaction->ticket_num : '';
                            $transaction_id = $waiting_transaction ? $waiting_transaction->id : '';
                            $w_t_id         = $waiting_transaction ? $waiting_transaction->work_transaction_id : '';
                            $writer->write($terminal_id, $waiting_transaction->id);
                            if ($comLayer) {
                                $this->comLayer($t_num, $comLayer, $branch_terminal);
                            }
                            if ($poe):
                                //$this->poe($t_num, $poe, $branch_terminal, $service_audio);
                            endif;
                            if ($mqtt):
                                //$this->mqtt($t_num, $mqtt, $branch_terminal, $service_audio);
                            endif;
                            if ($audio_controller && $service_audio):
                                $this->audio_controller($t_num, $audio_controller, $branch_terminal);
                            endif;
                        }
                    }
                } else {
                    $service_id    = $this->M_terminal->serve_ticket($id);
                    $service_audio = false;
                    $service_info  = $this->db->select('sound_muted')->get_where('service', ['id' => $service_id])->row();
                    if ($service_info && ! $service_info->sound_muted) {
                        $service_audio = true;
                    }
                    $t_num          = $this->M_terminal->get_ticket_num($id);
                    $transaction_id = $t_num ? $t_num->id : '';
                    $w_t_id         = $t_num ? $t_num->work_transaction_id : '';
                    $t_num          = $t_num ? $t_num->ticket_num : '';
                    $writer->write($terminal_id, $t_num->ticket_num);
                    if ($comLayer) {
                        $this->comLayer($t_num, $comLayer, $branch_terminal);
                    }
                    if ($poe):
                        $this->poe($t_num, $poe, $branch_terminal, $service_audio);
                    endif;
                    if ($mqtt):
                        $this->mqtt($t_num, $mqtt, $branch_terminal, $service_audio);
                    endif;
                    if ($audio_controller && $service_audio):
                        $this->audio_controller($t_num, $audio_controller, $branch_terminal);
                    endif;
                }
                if ($serving_ticket) {
                    record_log('Close Current Ticket with num. ' . $serving_ticket->ticket_num . ' and start serve the Next One with num. ' . $t_num);
                    $s_w_t_id         = $serving_ticket->work_transaction_id;
                    $s_ticket_num     = $serving_ticket->ticket_num;
                    $s_transaction_id = $serving_ticket->id;
                    $msg              = 'Close Current Ticket with num: ' . $s_ticket_num . ' at ' . date('Y-m-d H:i:s');
                    row_data_log($s_w_t_id, $s_transaction_id, $s_ticket_num, $msg);
                }
                record_log('Start serving the Next ticket with number:' . $t_num);
                $msg = 'Start serving the Next ticket with number: ' . $t_num . ' at ' . date('Y-m-d H:i:s');
                row_data_log($w_t_id, $transaction_id, $t_num, $msg);
                $settings = $this->db->get('OrganisationGlobalSettings')->row();
                if ($settings) {
                    $service_audio = false;
                    $service_info  = $this->db->select('sound_muted')->get_where('service', ['id' => $service_id])->row();
                    if ($service_info && ! $service_info->sound_muted) {
                        $service_audio = true;
                    }
                    $enable_audio = ! empty($settings->enable_audio_api) && ! empty($settings->audio_api_url) ? true : false;
                    $audio_api    = $enable_audio && $service_audio ? $settings->audio_api_url : null;
                    if ($audio_api && $service_id) {
                        $post_data = [
                            'windowNumber'  => $branch_terminal->window_no,
                                                                            //'serviceNumber' => $service_id,
                            'serviceNumber' => $branch_terminal->window_no, //$service_id,
                            'clientNumber'  => $t_num,
                        ];
                        connect_to_remote_post($audio_api, $post_data);
                    } elseif (! empty($settings->mqtt_broker_ip)) {
                        $server    = $settings->mqtt_broker_ip; // change if necessary
                        $port      = 1883;                      // change if necessary
                        $username  = '';                        // set your username
                        $password  = '';                        // set your password
                        $client_id = 'phpMQTT-publisher';       // make sure this is unique for connecting to sever - you could use uniqid()
                        $this->load->library("phpMQTT", [$server, $port, $client_id, null]);
                        $mqtt = $this->phpmqtt;
                        if ($mqtt->connect(true, null, $username, $password)) {
                            $terminalId   = 1;
                            $sDisplayId   = 1;
                            $audioId      = 1;
                            $displayType  = 2;
                            $sDisplayType = 2;
                            $mqtt->publish('WAM2/CH2_TO_GW', '$0D0' . $terminalId . ',' . ($displayType == 2 ? 13 : 11) . ',' . $t_num . '*6A', 0, true);
                            sleep(1);
                            $mqtt->publish('WAM2/CH2_TO_GW', '$0S0' . $sDisplayId . ',' . ($sDisplayType == 2 ? 13 : 11) . ',' . ($t_num . '-' . $terminalId) . '*6A', 0, true);
                            if ($service_audio) {
                                sleep(1);
                                $mqtt->publish('WAM2/CH2_TO_GW', '$0A0' . $audioId . ',46,E' . ($t_num . '-' . $terminalId) . '*6A', 0, true);
                            }
                            $mqtt->close();
                        }
                    }
                }
                echo 'ok';
                //redirect(base_url() . 'Settings/transactions_view');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                echo $error['text'];
                //$this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            echo $error['text'];
            //$this->load->view('private/page-500', $error);
        }
    }

    private function comLayer($t_num, $comLayer, $branch_terminal)
    {
        if (! empty($comLayer['url']) && (! empty($comLayer['cid']) || ! empty($comLayer['sid']))) {
            //var_export($comLayer);
            //die;
            $this->load->helper('hw');
            $reply         = ['requestType' => 'REP', 'sourceID' => $branch_terminal->window_no, 'command' => null, 'data1' => "", 'data2' => "", 'marker' => 'END'];
            $window_no     = str_pad($branch_terminal->window_no, 2, '0', STR_PAD_LEFT);
            $ticket_no     = str_pad($t_num, 4, '0', STR_PAD_LEFT);
            $jds_ticket_no = str_pad($t_num, 4, '0', STR_PAD_LEFT);
            if (! empty($comLayer['cid'])) {
                // Display Ticket Reply
                $reply['sourceID'] = $comLayer['cid'];
                $reply['command']  = 'DISP';
                $reply['data1']    = "{$ticket_no}";
                send_reply($comLayer['url'], $reply);
            }
            if (! empty($comLayer->sid)) {
                // JUMBO Display Ticket Reply
                $reply['sourceID'] = $comLayer['sid'];
                $reply['command']  = 'JDIS';
                $reply['data1']    = "{$jds_ticket_no}";
                $reply['data2']    = "{$window_no}";
                send_reply($comLayer['url'], $reply);
            }
        }
    }

    private function poe($t_num, $poe, $branch_terminal, $service_audio)
    {
        if ($t_num && ! empty($poe['ip']) && ! empty($poe['org']) && ! empty($poe['branch'])) {
            $server    = $poe['ip']; // change if necessary
            $port      = 1883;       // change if necessary
            $username  = '';         // set your username
            $password  = '';         // set your password
            $client_id = uniqid();   // make sure this is unique for connecting to sever - you could use uniqid()
            $this->load->library("phpMQTT", [$server, $port, $client_id, null]);
            $mqtt             = $this->phpmqtt;
            $this->service_id = null;
            if ($mqtt->connect(true, null, $username, $password)) {
                $org_name    = $poe['org'];
                $branch_name = $poe['branch'];
                if (! empty($poe['cid'])) {
                    $cDisplayId = $poe['cid'];
                    $publish    = [
                        'CMD'  => 'disp',
                        'DATA' => $t_num,
                    ];
                    $mqtt->publish("OPW/$org_name/$branch_name/To/D/$cDisplayId", json_encode($publish), 0, true);
                    sleep(1);
                }
                if (! empty($poe['sid']) || ! empty($poe['aid'])) {
                    $window_no = str_pad($branch_terminal->window_no, 2, '0', STR_PAD_LEFT);
                    $ticket_no = str_pad($t_num, 3, '0', STR_PAD_LEFT);
                    $publish   = [
                        'CMD'    => 'Disp',
                        'Window' => $window_no,
                        'Client' => $ticket_no,
                    ];
                    if (! empty($poe['sid'])) {
                        $sDisplayId = $poe['sid'];
                        $mqtt->publish("OPW/$org_name/$branch_name/To/S/$sDisplayId", json_encode($publish), 0, true);
                        sleep(1);
                    }
                    if (! empty($poe['aid']) && $service_audio) {
                        $audioId         = $poe['aid'];
                        $publish['CMD']  = 'Play';
                        $publish['Lang'] = $lang;
                        $mqtt->publish("OPW/$org_name/$branch_name/To/A/$audioId", json_encode($publish), 0, true);
                    }
                }
                $mqtt->close();
            }
        }
    }

    private function mqtt($t_num, $mqttConfig, $branch_terminal, $service_audio)
    {
        if ($t_num && ! empty($mqttConfig['ip']) && ! empty($mqttConfig['port']) && ! empty($mqttConfig['topic']) && ! empty($mqttConfig['channel'])) {
            $server    = $mqttConfig['ip'];   // change if necessary
            $port      = $mqttConfig['port']; // change if necessary
            $username  = '';                  // set your username
            $password  = '';                  // set your password
            $client_id = uniqid();            // make sure this is unique for connecting to sever - you could use uniqid()
            $this->load->library("phpMQTT", [$server, $port, $client_id, null]);
            $mqtt             = $this->phpmqtt;
            $this->service_id = null;
            if ($mqtt->connect(true, null, $username, $password)) {
                $topic   = $mqttConfig['topic'];
                $channel = $mqttConfig['channel'];
                $window  = (int) $branch_terminal->window_no;
                if (! empty($mqttConfig['cid'])) {
                    $cDisplayId   = (int) $mqttConfig['cid'];
                    $cDisplayType = (int) $mqttConfig['cdtype'];
                    $mqtt->publish("{$topic}/{$channel}", "$0D0{$cDisplayId},{$cDisplayType},{$t_num}*6A", 0, true);
                    sleep(1);
                }
                if (! empty($mqttConfig['sid'])) {
                    $sDisplayId   = (int) $mqttConfig['sid'];
                    $sDisplayType = (int) $mqttConfig['sdtype'];
                    $mqtt->publish("{$topic}/{$channel}", "$0S0{$sDisplayId},{$sDisplayType},{$t_num}-{$window}*6A", 0, true);
                    sleep(1);
                }
                if (! empty($mqttConfig['aid']) && $service_audio) {
                    $audioId = (int) $mqttConfig['aid'];
                    $aLang   = ! empty($mqttConfig['alang']) && in_array($mqttConfig['alang'], ['E', 'A']) ? $mqttConfig['alang'] : "E";
                    $mqtt->publish("{$topic}/{$channel}", "$0A0{$audioId},46,{$aLang},{$t_num}-{$window}*6A", 0, true);
                    sleep(1);
                }
                $mqtt->close();
            }
        }
    }

    public function config()
    {
        $this->form_validation->set_rules('c_ip_1', 'Connection IP Digits Group 1', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('c_ip_2', 'Connection IP Digits Group 2', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('c_ip_3', 'Connection IP Digits Group 3', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('c_ip_4', 'Connection IP Digits Group 4', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('c_port', $this->lang->line('connection_port'), 'required|integer');

        $this->form_validation->set_rules('a_ip_1', 'Server IP Digits Group 1', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('a_ip_2', 'Server IP Digits Group 2', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('a_ip_3', 'Server IP Digits Group 3', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('a_ip_4', 'Server IP Digits Group 4', 'required|integer|less_than[256]');

        $this->form_validation->set_rules('ag_ip_1', 'GateWay IP Digits Group 1', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('ag_ip_2', 'GateWay IP Digits Group 2', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('ag_ip_3', 'GateWay IP Digits Group 3', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('ag_ip_4', 'GateWay IP Digits Group 4', 'required|integer|less_than[256]');

        $this->form_validation->set_rules('a_subnet_1', 'SubnetMask Digits Group 1', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('a_subnet_2', 'SubnetMask Digits Group 2', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('a_subnet_3', 'SubnetMask Digits Group 3', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('a_subnet_4', 'SubnetMask Digits Group 4', 'required|integer|less_than[256]');

        $this->form_validation->set_rules('audio_controller[port]', $this->lang->line('audio_controller_port'), 'required|integer');
        $this->form_validation->set_rules('audio_controller[aid]', $this->lang->line('audio_controller_player_id'), 'required|integer');
        $this->form_validation->set_rules('audio_controller[rate]', $this->lang->line('audio_controller_rate'), 'required|integer');
        if ($this->form_validation->run() == false) {
            $this->session->set_flashdata('error', validation_errors());
            CI_load_view::load_view('branch/audio_config', []);
        } else {

            $ip               = $this->input->post('c_ip_1') . '.' . $this->input->post('c_ip_2') . '.' . $this->input->post('c_ip_3') . '.' . $this->input->post('c_ip_4');
            $port             = (int) $this->input->post('c_port');
            $new_ip           = $this->input->post('a_ip_1') . ',' . $this->input->post('a_ip_2') . ',' . $this->input->post('a_ip_3') . ',' . $this->input->post('a_ip_4');
            $new_gip          = $this->input->post('ag_ip_1') . ',' . $this->input->post('ag_ip_2') . ',' . $this->input->post('ag_ip_3') . ',' . $this->input->post('ag_ip_4');
            $new_subnet       = $this->input->post('a_subnet_1') . ',' . $this->input->post('a_subnet_2') . ',' . $this->input->post('a_subnet_3') . ',' . $this->input->post('a_subnet_4');
            $new_port         = (int) $this->input->post('audio_controller[port]');
            $audio_id         = (int) $this->input->post('audio_controller[aid]');
            $new_rate         = (int) $this->input->post('audio_controller[rate]');
            $audio_controller = [
                'ip'   => $ip,
                'port' => $port,
            ];
            $command = '0V' . $audio_id . ',10,' . $new_rate . ',' . $new_ip . ',' . $new_gip . ',' . $new_subnet . ',' . $new_port;
            $crc     = $this->generate_crc('$' . $command . '*');
            $message = '$' . $command . '*' . $crc;
            $this->connect_tcp($audio_controller, $message);
            echo $message;
        }
    }

    public function controller_settings()
    {
        $this->form_validation->set_rules('c_ip_1', 'Connection IP Digits Group 1', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('c_ip_2', 'Connection IP Digits Group 2', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('c_ip_3', 'Connection IP Digits Group 3', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('c_ip_4', 'Connection IP Digits Group 4', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('c_port', $this->lang->line('connection_port'), 'required|integer');
        $this->form_validation->set_rules('audio_controller[aid]', $this->lang->line('audio_controller_player_id'), 'required|integer');
        $this->form_validation->set_rules('audio_controller[ding]', $this->lang->line('audio_controller_ding'), 'required|integer');
        $this->form_validation->set_rules('audio_controller[ctype]', $this->lang->line('audio_controller_ctype'), 'required|integer');
        $this->form_validation->set_rules('audio_controller[wtype]', $this->lang->line('audio_controller_wtype'), 'required|integer');
        $this->form_validation->set_rules('audio_controller[volume]', $this->lang->line('audio_controller_volume'), 'required|integer|less_than[101]');
        if ($this->form_validation->run() == false) {
            $this->session->set_flashdata('error', validation_errors());
            CI_load_view::load_view('branch/audio_settings', []);
        } else {
            $ip               = $this->input->post('c_ip_1') . '.' . $this->input->post('c_ip_2') . '.' . $this->input->post('c_ip_3') . '.' . $this->input->post('c_ip_4');
            $port             = (int) $this->input->post('c_port');
            $audio_id         = (int) $this->input->post('audio_controller[aid]');
            $ding             = (int) $this->input->post('audio_controller[ding]');
            $client           = (int) $this->input->post('audio_controller[ctype]');
            $window           = (int) $this->input->post('audio_controller[wtype]');
            $volume           = (int) $this->input->post('audio_controller[volume]');
            $audio_controller = [
                'ip'   => $ip,
                'port' => $port,
            ];
            $command = '0A' . str_pad($audio_id, 2, '0', STR_PAD_LEFT) . ',44,' . $client . '-' . $window . '-' . $ding . '-' . $volume;
            $crc     = $this->generate_crc('$' . $command . '*');
            $message = '$' . $command . '*' . $crc;
            $this->connect_tcp($audio_controller, $message);
            echo $message;
        }
    }

    public function mac_settings()
    {
        $this->form_validation->set_rules('c_ip_1', 'Connection IP Digits Group 1', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('c_ip_2', 'Connection IP Digits Group 2', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('c_ip_3', 'Connection IP Digits Group 3', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('c_ip_4', 'Connection IP Digits Group 4', 'required|integer|less_than[256]');
        $this->form_validation->set_rules('c_port', $this->lang->line('connection_port'), 'required|integer');
        $this->form_validation->set_rules('audio_controller[aid]', $this->lang->line('audio_controller_player_id'), 'required|integer');
        $this->form_validation->set_rules('mac', $this->lang->line('audio_controller_mac'), 'required|callback_mac_check');
        if ($this->form_validation->run() == false) {
            $this->session->set_flashdata('error', validation_errors());
            CI_load_view::load_view('branch/audio_settings_mac', []);
        } else {
            $ip               = $this->input->post('c_ip_1') . '.' . $this->input->post('c_ip_2') . '.' . $this->input->post('c_ip_3') . '.' . $this->input->post('c_ip_4');
            $port             = (int) $this->input->post('c_port');
            $audio_id         = (int) $this->input->post('audio_controller[aid]');
            $mac              = strtoupper($this->input->post('mac'));
            $new_mac          = explode(':', $mac);
            $audio_controller = [
                'ip'   => $ip,
                'port' => $port,
            ];
            $command = '0V' . str_pad($audio_id, 2, '0', STR_PAD_LEFT) . ',12,' . $new_mac[0] . ':' . $new_mac[1] . ':' . $new_mac[2] . ':' . $new_mac[3] . ':' . $new_mac[4] . ':' . $new_mac[5];
            $crc     = $this->generate_crc('$' . $command . '*');
            $message = '$' . $command . '*' . $crc;
            $this->connect_tcp($audio_controller, $message);
            echo $message;
        }
    }

    public function mac_check($mac)
    {
        if (filter_var($mac, FILTER_VALIDATE_MAC)) {
            return true;
        }
        $this->form_validation->set_message('mac_check', 'Check {field} field format');
        return false;
    }

    public function test($ip, $port, $t_num, $window, $lang = 'A')
    {
        $lang                       = in_array($lang, ['A', 'E']) ? $lang : 'A';
        $branch_terminal            = new stdClass();
        $branch_terminal->window_no = $window;
        $audio_controller           = [
            'ip'   => $ip,
            'port' => $port,
            'aid'  => 1,
            'lang' => $lang,
        ];
        $message = $this->audio_controller($t_num, $audio_controller, $branch_terminal);
        echo $message;
    }

    private function audio_controller($t_num, $audio_controller, $branch_terminal)
    {
        $message = '';
        if ($t_num && ! empty($audio_controller['ip']) && ! empty($branch_terminal->window_no) && ! empty($audio_controller['port']) && ! empty($audio_controller['aid']) && ! empty($audio_controller['lang'])) {
            $window_no = $branch_terminal->window_no;
            $lang      = in_array($audio_controller['lang'], ['A', 'E']) ? $audio_controller['lang'] : 'A';
            $command   = '0A0' . $audio_controller['aid'] . ',46,' . $lang . $t_num . '-' . $window_no;
            $crc       = $this->generate_crc('$' . $command . '*');
            $message   = '$' . $command . '*' . $crc;
            $this->connect_tcp($audio_controller, $message);
        }
        return $message;
    }

    private function connect_tcp($audio_controller, $message)
    {
        $host = $audio_controller['ip'];
        $port = $audio_controller['port'];
        $fp   = fsockopen($host, $port, $errno, $errstr, 1);
        if ($fp) {                   // get the welcome message fgets ($fp, 1024); // write the user string to the socket
            fputs($fp, $message . "\r"); // get the result $result .= fgets ($fp, 1024); // close the connection
            fclose($fp);
        }
    }

    private function generate_crc($cmd)
    {
        $checksum = 0;
        for ($i = 0; $i < strlen($cmd); $i++):
            $checksum = $checksum ^ $this->JS_charCodeAt($cmd, $i);
        endfor;
        $hexsum = strtoupper(base_convert(intval($checksum), 10, 16));
        if (strlen($hexsum) < 2):
            $hexsum = substr("00" . $hexsum, -2);
        endif;
        return $hexsum;
    }

    private function JS_charCodeAt($str, $index)
    {
        $utf16 = mb_convert_encoding($str, 'UTF-16LE', 'UTF-8');
        return ord($utf16[$index * 2]) + (ord($utf16[$index * 2 + 1]) << 8);
    }

    public function send_notification_before_turn_with_x($services_num_arr, $calling_mechanism)
    {
        $this->db->select('custmers_count');
        $notifications_settings = $this->db->get('notification_content')->row();
        if (! $notifications_settings) {
            return;
        }
        $transactions = [];

        for ($i = 0; $i < $notifications_settings->custmers_count; $i++) {
            $transaction = $this->M_terminal->get_waiting_transactions($services_num_arr, $calling_mechanism, true);
            if (empty($transaction)) {
                return;
            }

            array_push($transactions, $transaction->id);

            $this->db->update('transactions', ['fake_status' => 2, 'fake_its_turn' => 0], ['id' => $transaction->id]);

            if ($i != $notifications_settings->custmers_count - 1) {
                $this->db->update('transactions', ['fake_its_turn' => 1], ['work_transaction_id' => $transaction->work_transaction_id, 'order' => $transaction->order + 1]);
            }
        }

        for ($i = 0; $i < count($transactions); $i++) {
            if ($i == 0) {
                $this->db->update('transactions', ['fake_status' => 0, 'fake_its_turn' => 1], ['id' => $transactions[$i]]);
            } else {
                $this->db->update('transactions', ['fake_status' => 0, 'fake_its_turn' => 0], ['id' => $transactions[$i]]);
            }
        }

        if ($transaction->turn_sms_sent == 0) {
            $work_transactions = $this->db->get_where('work_transactions', ['key' => $transaction->work_transaction_key])->row();
            if (! $work_transactions) {
                return;
            }

            $this->Crud_model->send_customer_notifications($work_transactions->cus_phone, $work_transactions->cus_reserv, 'before_turn', '');
        }
        $this->db->update('transactions', ['turn_sms_sent' => 1], ['id' => $transaction->id]);
    }

    public function Hold_ticket($id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("transactions_lvl", "v")) {
                $ticket_num  = $this->M_terminal->Hold_ticket($id);
                $writer      = $this->config_writer->get_instance();
                $terminal_id = "current_serve_ticket_" . $this->session->userdata('terminal_id_at_table');
                $writer->write($terminal_id, '');
                record_log('Hold Current Ticket with num.:' . $ticket_num);
                $transaction = $this->db->get_where('transactions', ['id' => $id])->row();
                if (! empty($transaction)) {
                    $w_t_id     = $transaction->work_transaction_id;
                    $ticket_num = $transaction->ticket_num;
                    $msg        = 'Hold Current Ticket with num: ' . $ticket_num . ' at ' . date('Y-m-d H:i:s');
                    row_data_log($w_t_id, $transaction->id, $ticket_num, $msg);
                }
                echo 'ok';
                //redirect(base_url() . 'Settings/transactions_view');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                echo $error['text'];
                //$this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            echo $error['text'];
            //$this->load->view('private/page-500', $error);
        }
    }

    public function retuen_to_queue_ticket($id)
    {
        if ($this->session->userdata('login')) {
            $ticket_num  = $this->M_terminal->back_to_queue_ticket($id);
            $writer      = $this->config_writer->get_instance();
            $terminal_id = "current_serve_ticket_" . $this->session->userdata('terminal_window_no');
            $writer->write($terminal_id, '');
            record_log('Ticket back to the queue with num.:' . $ticket_num . ' from window no.:' . $this->session->userdata('terminal_id_at_table'));
            $transaction = $this->db->get_where('transactions', ['id' => $id])->row();
            if (! empty($transaction)) {
                $w_t_id = $transaction->work_transaction_id;
                $msg    = 'Ticket back to the queue with num: ' . $ticket_num . ' at ' . date('Y-m-d H:i:s');
                row_data_log($w_t_id, $transaction->id, $ticket_num, $msg);
            }
            echo 'ok';
            //redirect(base_url() . 'Settings/transactions_view');
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            echo $error['text'];
            //$this->load->view('private/page-500', $error);
        }
    }

    public function Resume_ticket($id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("transactions_lvl", "v")) {
                $serving_ticket = $this->M_terminal->get_serving_ticket();
                if ($serving_ticket) {
                    $this->M_terminal->close_current_ticket($serving_ticket->id);
                }
                $this->M_terminal->Resume_ticket($id);
                $t_num       = $this->M_terminal->get_ticket_num($id);
                $writer      = $this->config_writer->get_instance();
                $terminal_id = "current_serve_ticket_" . $this->session->userdata('terminal_id_at_table');
                $writer->write($terminal_id, $t_num->ticket_num);
                record_log('Resume holded Ticket with num.:' . ($t_num ? $t_num->ticket_num : ''));
                $transaction = $this->db->get_where('transactions', ['id' => $id])->row();
                if (! empty($transaction)) {
                    $w_t_id     = $transaction->work_transaction_id;
                    $ticket_num = $transaction->ticket_num;
                    $msg        = 'Resume holded Ticket with num: ' . $ticket_num . ' at ' . date('Y-m-d H:i:s');
                    row_data_log($w_t_id, $transaction->id, $ticket_num, $msg);
                }
                echo 'ok';
                //redirect(base_url() . 'Settings/transactions_view');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                echo $error['text'];
                //$this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            echo $error['text'];
            //$this->load->view('private/page-500', $error);
        }
    }

    public function convert_time_to_sec($time)
    {
        $time_arr    = explode(":", $time);
        $time_in_sec = $time_arr[0] * 60 * 60 + $time_arr[1] * 60 + $time_arr[2];
        return $time_in_sec;
    }

    public function format_time($time_in_sec)
    {
        if ($time_in_sec > 3600) {
            $hours = intval($time_in_sec / 3600);
            if (strlen((string) $hours) < 2) {
                $hours = '0' . $hours;
            }
            $min = intval(($time_in_sec % 3600) / 60);
            if (strlen((string) $min) < 2) {
                $min = '0' . $min;
            }
            $sec = ($time_in_sec % 3600) % 60;
            if (strlen((string) $sec) < 2) {
                $sec = '0' . $sec;
            }
            return $hours . ':' . $min . ':' . $sec;
        } elseif ($time_in_sec > 60) {
            $min = intval($time_in_sec / 60);
            if (strlen((string) $min) < 2) {
                $min = '0' . $min;
            }
            $sec = $time_in_sec % 60;
            if (strlen((string) $sec) < 2) {
                $sec = '0' . $sec;
            }
            return '00:' . $min . ':' . $sec;
        } else {
            $sec = intval($time_in_sec);
            if (strlen((string) $sec) < 2) {
                $sec = '0' . $sec;
            }
            return '00:00:' . $sec;
        }
    }

    public function end_current_ticket($id)
    {
        if ($this->session->userdata('login')) {
            if ($id) {
                $ticket_num  = $this->M_terminal->close_current_ticket($id);
                $writer      = $this->config_writer->get_instance();
                $terminal_id = "current_serve_ticket_" . $this->session->userdata('terminal_id_at_table');
                $writer->write($terminal_id, '');
                record_log('End serving ticket num.:' . $ticket_num);
                $transaction = $this->db->get_where('transactions', ['id' => $id])->row();
                if (! empty($transaction)) {
                    $transaction_id = $transaction->id;
                    $w_t_id         = $transaction->work_transaction_id;
                    $ticket_num     = $transaction->ticket_num;
                    $msg            = 'End serving ticket num: ' . $transaction->ticket_num . ' at ' . date('Y-m-d H:i:s');
                    row_data_log($w_t_id, $transaction_id, $ticket_num, $msg);
                }
                echo 'ok';
                //redirect(base_url() . 'Settings/transactions_view');
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            echo $error['text'];
            //$this->load->view('private/page-500', $error);
        }
    }

    public function do_transfer_ticket($transaction_id, $branch_terminal, $force_transfer = false)
    {
        if ($this->session->userdata('login')) {
            if (get_p("transactions_lvl", "v")) {
                date_default_timezone_set('Africa/Cairo');
                $today                    = date('Y-m-d H:i:s');
                $services_num_arr         = [];
                $transaction_data         = $this->Crud_model->get_one('transactions', $transaction_id);
                $specific_branch_terminal = $this->M_terminal->get_specific_branch_terminal($branch_terminal);
                if ($transaction_data) {
                    $can_transfer = false;

                    if ($force_transfer) {
                        // Force transfer regardless of service assignment
                        $can_transfer = true;
                        record_log('Force transfer initiated for ticket: ' . $transaction_data->ticket_num);
                    } else {
                        // Check normal service assignment
                        $terminal_has_services = $this->M_terminal->get_terminal_has_services($specific_branch_terminal->id);
                        if ($terminal_has_services) {
                            foreach ($terminal_has_services as $service) {
                                if (! in_array($service->service_id, $services_num_arr)) {
                                    array_push($services_num_arr, $service->service_id);
                                }
                            }
                            // Check if terminal has this service
                            $can_transfer = in_array($transaction_data->service_id, $services_num_arr);
                        }
                    }

                    if ($can_transfer) {
                        // Do transfer
                        $inserted_data = [
                            'serving_time'         => '00:00:00',
                            'waiting_time'         => '00:00:00',
                            'terminal_id'          => $branch_terminal,
                            'terminal_key'         => $specific_branch_terminal->key,
                            'client_name'          => $transaction_data->client_name,
                            'client_id_num'        => $transaction_data->client_id_num,
                            'entering_time'        => $today,
                            'service_id'           => $transaction_data->service_id,
                            'branch_id'            => $transaction_data->branch_id,
                            'date'                 => $transaction_data->date,
                            'ticket_num'           => $transaction_data->ticket_num,
                            'status'               => 0,
                            'cname'                => $transaction_data->cname,
                            'vr_service_name'      => $transaction_data->vr_service_name,
                            'work_transaction_id'  => $transaction_data->work_transaction_id,
                            'work_transaction_key' => $transaction_data->work_transaction_key,
                            'order'                => $transaction_data->order,
                            'is_transfered'        => 2,
                            'transfer_details'     => $transaction_data->terminal_id,
                            'its_turn'             => 1,
                            'fake_its_turn'        => 1,
                        ];
                        $new_ticket             = $this->Crud_model->insert('transactions', $inserted_data);
                        $data                   = [];
                        $data['transaction_id'] = $new_ticket;
                        $data['branch_id']      = $transaction_data->branch_id;
                        $data['ticket_num']     = $transaction_data->ticket_num;
                        $data['vr_service']     = $transaction_data->service_id;
                        $data['counter']        = 5000;
                        $this->db->insert('escalate_manual', $data);
                        record_log('Insert escalation with ticket number : ' . $data['ticket_num']);
                        $this->Crud_model->update('transactions', $transaction_id, ['is_transfered' => 1, 'status' => 3, 'its_turn' => 0, 'fake_its_turn' => 0]);
                        $transfer_type = $force_transfer ? ' (Force Transfer)' : '';
                        record_log('Transfer Ticket From Terminal' . $transaction_data->terminal_id . ' To Terminal' . $branch_terminal . $transfer_type);
                        $transaction_id = $transaction_data->id;
                        $w_t_id         = $transaction_data->work_transaction_id;
                        $msg            = 'Transfer ticket with number: ' . $transaction_data->ticket_num . ' at ' . date('Y-m-d H:i:s') . $transfer_type;
                        row_data_log($w_t_id, $transaction_id, $transaction_data->ticket_num, $msg);
                        $this->session->set_flashdata('msg', $this->lang->line('ticket_transfered'));
                        redirect(base_url() . 'Settings/transactions_view');
                    } else {
                        record_log('Failed to transfer ticket : Transferred to ' . $branch_terminal . ' terminal that not support this service');
                        $transaction_id = $transaction_data->id;
                        $w_t_id         = $transaction_data->work_transaction_id;
                        $msg            = 'Failed to transfer ticket with number: ' . $transaction_data->ticket_num . ' at ' . date('Y-m-d H:i:s');
                        row_data_log($w_t_id, $transaction_id, $transaction_data->ticket_num, $msg);
                        $this->session->set_flashdata('warning', $this->lang->line('not_serving'));
                        redirect(base_url() . 'Settings/transactions_view');
                    }
                    echo 'ok';
                    //redirect(base_url() . 'Settings/transactions_view');
                }
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                echo $error['text'];
                //$this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            echo $error['text'];
            //$this->load->view('private/page-500', $error);
        }
    }

    public function do_force_transfer_ticket($transaction_id, $branch_terminal)
    {
        return $this->do_transfer_ticket($transaction_id, $branch_terminal, true);
    }

    public function serve_ticket_force_display($id = 0)
    {
        if ($this->session->userdata('login')) {
            if (get_p("serve_waiting_lvl", "v")) {
                $serving_ticket = $this->M_terminal->get_serving_ticket();
                if ($serving_ticket) {
                    $this->M_terminal->close_current_ticket($serving_ticket->id);
                }
                $service_id       = null;
                $writer           = $this->config_writer->get_instance();
                $terminal_id      = "current_serve_ticket_" . $this->session->userdata('terminal_id_at_table');
                $t_num            = '---';
                $branch_terminal  = $this->M_terminal->get_branch_terminal();
                $comLayer         = $branch_terminal ? unserialize($branch_terminal->comLayer) : null;
                $mqtt             = $branch_terminal ? unserialize($branch_terminal->mqtt) : null;
                $poe              = $branch_terminal ? unserialize($branch_terminal->poe) : null;
                $audio_controller = $branch_terminal ? unserialize($branch_terminal->audio_controller) : null;

                if ($id == 0) {
                    $transaction_id   = '';
                    $services_num_arr = []; // Empty array to force display all services
                    if ($branch_terminal) {
                        // get from transactions with force display (bypass service assignment)
                        $calling_mechanism   = terminal_calling_technique($branch_terminal);
                        $waiting_transaction = $this->M_terminal->get_waiting_transactions(
                            $services_num_arr,
                            $calling_mechanism,
                            true,
                            null,
                            null,
                            null,
                            null,
                            false,
                            true // force_all = true to bypass service assignment
                        );

                        if ($waiting_transaction) {
                            $service_id    = $this->M_terminal->serve_ticket($waiting_transaction->id);
                            $service_audio = false;
                            $service_info  = $this->db->select('sound_muted')->get_where('service', ['id' => $service_id])->row();
                            if ($service_info && ! $service_info->sound_muted) {
                                $service_audio = true;
                            }
                            $t_num          = $waiting_transaction ? $waiting_transaction->ticket_num : '';
                            $transaction_id = $waiting_transaction ? $waiting_transaction->id : '';
                            $w_t_id         = $waiting_transaction ? $waiting_transaction->work_transaction_id : '';
                            $writer->write($terminal_id, $waiting_transaction->id);
                            if ($comLayer) {
                                $this->comLayer($t_num, $comLayer, $branch_terminal);
                            }
                            if ($poe):
                                //$this->poe($t_num, $poe, $branch_terminal, $service_audio);
                            endif;
                            if ($mqtt):
                                //$this->mqtt($t_num, $mqtt, $branch_terminal, $service_audio);
                            endif;
                            if ($audio_controller && $service_audio):
                                $this->audio_controller($t_num, $audio_controller, $branch_terminal);
                            endif;
                        }
                    }
                } else {
                    // Serve specific ticket by ID
                    $service_id    = $this->M_terminal->serve_ticket($id);
                    $service_audio = false;
                    $service_info  = $this->db->select('sound_muted')->get_where('service', ['id' => $service_id])->row();
                    if ($service_info && ! $service_info->sound_muted) {
                        $service_audio = true;
                    }
                    $t_num          = $this->M_terminal->get_ticket_num($id);
                    $transaction_id = $t_num ? $t_num->id : '';
                    $w_t_id         = $t_num ? $t_num->work_transaction_id : '';
                    $t_num          = $t_num ? $t_num->ticket_num : '';
                    $writer->write($terminal_id, $t_num);
                    if ($comLayer) {
                        $this->comLayer($t_num, $comLayer, $branch_terminal);
                    }
                    if ($poe):
                        $this->poe($t_num, $poe, $branch_terminal, $service_audio);
                    endif;
                    if ($mqtt):
                        $this->mqtt($t_num, $mqtt, $branch_terminal, $service_audio);
                    endif;
                    if ($audio_controller && $service_audio):
                        $this->audio_controller($t_num, $audio_controller, $branch_terminal);
                    endif;
                }

                // Log the force display action
                record_log('Force display serve ticket: ' . $t_num . ' at terminal: ' . $this->session->userdata('terminal_window_no'));

                // Return response
                echo json_encode(['status' => 'success', 'ticket_num' => $t_num, 'force_display' => true]);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                echo $error['text'];
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            echo $error['text'];
        }
    }

    public function get_all_waiting_tickets()
    {
        if ($this->session->userdata('login')) {
            if (get_p("serve_waiting_lvl", "v")) {
                $branch_terminal = $this->M_terminal->get_branch_terminal();
                if ($branch_terminal) {
                    $services_num_arr = []; // Empty array
                    $calling_mechanism = terminal_calling_technique($branch_terminal);

                    // Get all waiting transactions regardless of service assignment
                    $waiting_transactions = $this->M_terminal->get_waiting_transactions(
                        $services_num_arr,
                        $calling_mechanism,
                        false, // get all, not just one
                        null,
                        null,
                        null,
                        null,
                        false,
                        true // force_all = true to bypass service assignment
                    );

                    $data = [
                        'waiting_transactions' => $waiting_transactions,
                        'force_display' => true,
                        'terminal_window_no' => $this->session->userdata('terminal_window_no')
                    ];

                    echo json_encode($data);
                } else {
                    echo json_encode(['error' => 'Terminal not found']);
                }
            } else {
                echo json_encode(['error' => 'Permission denied']);
            }
        } else {
            echo json_encode(['error' => 'Not logged in']);
        }
    }

    public function do_millensys_ticket($transaction_id, $millensys_service)
    {
        if ($this->session->userdata('login')) {
            if (get_p("transactions_lvl", "v")) {
                date_default_timezone_set('Africa/Cairo');
                $transaction_data = $this->Crud_model->get_one('transactions', $transaction_id);
                $service          = $this->db->get_where('service', ['id' => $millensys_service, 'enable_millensys' => 1])->row();
                if ($transaction_data && $service) {
                    $config           = $this->db->select('enable_millensys,millensys_url')->get('OrganisationGlobalSettings')->row();
                    $enable_millensys = $config && $config->enable_millensys && $config->millensys_url;
                    $millensys_url    = $enable_millensys ? $config->millensys_url : null;
                    $millensys_number = null;
                    if ($enable_millensys):
                        $params = [
                            'queueNo'           => $transaction_data->ticket_num,
                            'queueDatetime'     => date('dmYHis'),
                            'TicketServiceName' => $service->service_name,
                        ];
                        $millensys = connect_to_remote_get($millensys_url, $params);
                        if (! empty($millensys['TicketNo'])):
                            $millensys_number = $millensys['TicketNo'];
                        endif;
                    endif;
                    if ($millensys_number) {
                        $data = [
                            'transaction_id' => $transaction_data->id,
                            'ticket_num'     => $transaction_data->ticket_num,
                            'CustomerNo'     => $millensys_number,
                            'ServiceName'    => $service->service_name,
                            'created'        => date('Y-m-d H:i:s'),
                        ];
                        $this->Crud_model->insert('millensys_transactions', $data);
                        $this->Crud_model->update('transactions', $transaction_id, ['millensys_number' => $millensys_number]);
                        $msg = 'Millensys ticket with number: ' . $millensys_number . ' for ticket number: ' . $transaction_data->ticket_num . ' at ' . date('Y-m-d H:i:s');
                        record_log($msg);
                        $transaction_id = $transaction_data->id;
                        $w_t_id         = $transaction_data->work_transaction_id;
                        row_data_log($w_t_id, $transaction_id, $transaction_data->ticket_num, $msg);
                        $this->session->set_flashdata('msg', $this->lang->line('millensys_ticket_requested'));
                        record_log("ticket generated with number: " . $Work_transaction_id[1]);
                        $general_settings = $this->db->get('OrganisationGlobalSettings')->row();
                        $branch_id        = $this->session->userdata('branch_id');
                        $branch_data      = $this->db->get_where('Branch', ['BranchID' => $branch_id])->row();
                        $ticket_sett      = $this->db->get_where('vr_ticket_sett', ['template_id' => $branch_id, 'ticket_lang' => 'english'])->row();
                        $ticket_header_ad = $this->M_templates->get_header_ad($branch_id, $ticket_sett->ticket_lang);
                        $ticket_footer_ad = $this->M_templates->get_footer_ad($branch_id, $ticket_sett->ticket_lang);

                        $arr = ['general_settings' => $general_settings, 'ticket_settings'  => $ticket_sett,
                            'services'                      => [
                                $service->service_name,
                            ], 'branch_data'                                                         => $branch_data,
                            'ticket_num'                    => $millensys_number, 'ticket_footer_ad' => $ticket_footer_ad, 'ticket_header_ad' => $ticket_header_ad, 'branch_id' => $branch_id];
                        $this->load->view('transactions/ticket_millensys', $arr);
                    } else {
                        $this->session->set_flashdata('warning', $this->lang->line('millensys_ticket_request_failed'));
                        redirect(base_url() . 'Settings/transactions_view');
                    }
                }
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function set_terminal_idle($terminal_id)
    {
        if ($this->session->userdata('login')) {
            if ($terminal_id) {
                $terminal_data = $this->Crud_model->get_one('terminal', $terminal_id);
                if ($terminal_data) {
                    $this->Crud_model->update('terminal', $terminal_id, ['idle_state' => 0, 'status_change_from' => date('Y-m-d H:i:s')]);
                    record_log('Terminal' . $terminal_data->window_no . ' is set as idle');
                    $this->session->set_flashdata('msg', $this->lang->line('set_workstation_idle'));
                    redirect(base_url() . 'Settings/transactions_view');
                }
            }
        }
    }

    public function set_terminal_idle_active($terminal_id, $status)
    {
        if ($this->session->userdata('login')) {
            if ($terminal_id) {
                $terminal_data = $this->Crud_model->get_one('terminal', $terminal_id);
                if ($terminal_data) {
                    $this->Crud_model->update('terminal', $terminal_id, ['idle_state' => $status, 'status_change_from' => date('Y-m-d H:i:s')]);
                    if ($status == 0) {
                        record_log('Terminal' . $terminal_data->window_no . ' is set as idle');
                        $this->session->set_flashdata('msg', $this->lang->line('set_workstation_idle'));
                    } else {
                        record_log('Terminal' . $terminal_data->window_no . ' is set as active');
                        $this->session->set_flashdata('msg', $this->lang->line('set_workstation_active'));
                    }
                    redirect(base_url() . 'Settings/transactions_view');
                }
            }
        }
    }

    public function add_remove_ticket($transaction_id, $status)
    {
        if ($this->session->userdata('login') && get_p("serve_waiting_lvl", "v")) {
            if ($transaction_id) {
                $transaction_data = $this->Crud_model->get_one('transactions', $transaction_id);
                if ($transaction_data) {
                    if ($status == 0) {
                        $this->Crud_model->update('transactions', $transaction_id, ['terminal_id' => 0, 'terminal_key' => '', 'agent_id' => 0]);
                        record_log('Remove Ticket From Terminal ' . $this->session->userdata('terminal_window_no'));
                        $w_t_id     = $transaction_data->work_transaction_id;
                        $ticket_num = $transaction_data->ticket_num;
                        $msg        = 'Remove ticket from terminal with num: ' . $ticket_num . ' at ' . date('Y-m-d H:i:s');
                        row_data_log($w_t_id, $transaction_data->id, $ticket_num, $msg);
                        $this->session->set_flashdata('msg', $this->lang->line('ticket_removed_queue'));
                    } else {
                        $terminal_key = $this->db->get_where('terminal', ['id' => $this->session->userdata('terminal_id_at_table')])->row()->key;
                        $this->Crud_model->update('transactions', $transaction_id, ['terminal_id' => $this->session->userdata('terminal_window_no'), 'terminal_key' => $terminal_key, 'agent_id' => $this->session->userdata('login')]);
                        record_log('Add Ticket To Terminal ' . $this->session->userdata('terminal_window_no'));
                        $w_t_id     = $transaction_data->work_transaction_id;
                        $ticket_num = $transaction_data->ticket_num;
                        $msg        = 'Add ticket to Terminal with num: ' . $ticket_num . ' at ' . date('Y-m-d H:i:s');
                        row_data_log($w_t_id, $transaction_data->id, $ticket_num, $msg);
                        $this->session->set_flashdata('msg', $this->lang->line('ticket_added_queue'));
                    }
                    redirect(base_url() . 'Settings/transactions_view');
                }
            }
        }
    }

    public function workstation($ticket_num = null, $branch = null, $terminal_wn = null)
    {
        if (! $this->require_login && ! empty($branch) && ! empty($terminal_wn)) {
            $this->session->set_userdata('branch_id', $branch);
            $this->session->set_userdata('terminal_window_no', $terminal_wn);
        }
        if ($this->session->userdata('terminal_window_no') && $this->session->userdata('branch_id') != 0) {
            $branch_id                = $this->session->userdata('branch_id');
            $arr['data']              = $this->M_terminal->get_workstation_settings($branch_id);
            $arr['currently_serving'] = $this->M_terminal->get_currently_serving($this->session->userdata('terminal_window_no'));
            $arr['slideshow_imgs']    = $this->M_terminal->get_slideshow_imgs($branch_id);
            if (! $arr['data']) {
                $error['text'] = $this->lang->line('set_worstation_page_sett');
                $this->load->view('private/page-500', $error);
                return;
            } elseif (! is_numeric($arr['data']->refresh_rate)) {
                $error['text'] = $this->lang->line('refresh_rate_not_numeric');
                $this->load->view('private/page-500', $error);
                return;
            }
            $arr['active'] = 'workstation';

            $this->load->view('transactions/workstation', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function get_current_serve($terminal_id)
    {
        $this->load->config("array_current_serving");
        $vv[0] = $this->db->get_where('transactions', ['terminal_id' => $this->session->userdata('terminal_window_no'),
            'transactions.branch_id'                                          => $this->session->userdata('branch_id'),
            'transactions.status'                                             => 1])->row();
        $vv[0] = $vv[0] ? $vv[0]->ticket_num : '';
        $this->db->select('idle_state');
        $ter_row = $this->db->get_where('terminal', ['id' => $terminal_id])->row();
        $vv[1]   = $ter_row ? $ter_row->idle_state : 0;
        echo json_encode($vv);
    }

    public function get_currently_serving()
    {
        echo json_encode($this->M_terminal->get_currently_serving($this->session->userdata('terminal_window_no')));
    }

    public function waiting_area_display_old($waiting_area_id = null, $branch = null)
    {
        if (! $this->require_login || (get_p("waiting_area_display_lvl", "v") && $this->session->userdata('login'))) {
            if (! $this->require_login && ! empty($branch)) {
                $this->session->set_userdata('branch_id', $branch);
            }
            if ($this->session->userdata('branch_id') != 0) {
                $branch_id = $this->session->userdata('branch_id');
            } elseif ($this->session->userdata('branch_id') == 0 && $this->session->userdata('updated_new_branch') != 0) {
                $branch_id = $this->session->userdata('updated_new_branch');
            } else {
                $error['text'] = $this->lang->line('error_branch_select');
                $this->load->view('private/page-500', $error);
                return;
            }
            $arr['data']           = $this->M_terminal->get_waiting_area_settings($waiting_area_id);
            $arr['slideshow_imgs'] = $this->M_terminal->get_waiting_areas_slideshow_imgs($waiting_area_id); // area id
            if (! $arr['data']) {
                $error['text'] = $this->lang->line('set_waitingarea_page_sett');
                $this->load->view('private/page-500', $error);
                return;
            }
            $arr['serving_clients'] = $this->M_terminal->get_windows_with_serving($waiting_area_id, $branch_id, $arr['data']->sequence_of_display, $arr['data']->terminals_per_page);
            $arr['active']          = 'waiting_area_display';
            $arr['branch_id']       = $branch_id;
            $arr['waiting_area_id'] = $waiting_area_id;
            $arr['news']            = $this->M_terminal->get_branch_news($branch_id);
            $this->load->view('transactions/waitingarea', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function select_waiting_area()
    {
        if (isset($this->session->userdata("license")['lcd-based_waiting_area_display']) && $this->session->userdata("license")['lcd-based_waiting_area_display']['open'] == 1 && (check_is_standalone() || ! check_is_server()) && get_p("waiting_area_display_lvl", "v")) {
            if ($this->session->userdata('branch_id') != 0) {
                $branch_id = $this->session->userdata('branch_id');
            } elseif ($this->session->userdata('branch_id') == 0 && $this->session->userdata('updated_new_branch') != 0) {
                $branch_id = $this->session->userdata('updated_new_branch');
            } else {
                $error['text'] = $this->lang->line('error_branch_select');
                $this->load->view('private/page-500', $error);
                return;
            }
            $arr          = ['active' => 'waiting_area_display'];
            $arr['areas'] = $this->M_terminal->get_branch_waiting_areas($branch_id);
            CI_load_view::load_view('transactions/select_waiting_area', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function get_waiting_clients($waiting_area_id, $branch_id, $start_terminal)
    {
        $data = $this->M_terminal->get_waiting_area_settings($waiting_area_id);
        if (! $data) {
            return;
        }
        // get waiting screen assigned terminals
        $this->db->select('terminal.window_no,terminal.id');
        $this->db->join('terminal', 'terminal.key=waiting_area_terminals.terminal_key');
        $assigned_terminals = $this->db->get_where('waiting_area_terminals', ['waiting_area_id' => $waiting_area_id])->result();
        //$win_no_arr = array();
        $terminal_arr = [];
        foreach ($assigned_terminals as $terminal) {
            // array_push($win_no_arr, $terminal->window_no);
            array_push($terminal_arr, $terminal->id);
        }
        if (empty($terminal_arr)) { //empty($win_no_arr) ||
                                        //echo '0';
            return;
        }
        /////////////////////////////////////

        $serving_clients = $this->M_terminal->get_windows_with_serving($waiting_area_id, $branch_id, $data->sequence_of_display, $data->terminals_per_page, $start_terminal);
        $this->db->where_in('terminal.id', $terminal_arr);
        $branch_windows_count = $this->db->get_where('terminal', ['branch_id' => $branch_id, 'deleted' => 0, 'status' => 1])->num_rows();

        // actually now i don't make anything with it... $currenlty_serving_count
        $this->db->join('terminal ter', 'ter.window_no = t.terminal_id and ter.status = 1 and ter.branch_id = ' . $branch_id);
        $this->db->where_in('ter.id', $terminal_arr);
        $currenlty_serving_count = $this->db->get_where('transactions t', ['t.branch_id' => $branch_id, 't.status' => 1])->num_rows();
        echo json_encode([$serving_clients, $branch_windows_count, $currenlty_serving_count]);
    }

    // ========================= Agent KPIs ===========================================
    public function agent_kpis()
    {
        if ($this->session->userdata('User_type') == 'Agent' || get_p("agent_kpis_lvl", "v")) {
            $session_id            = $this->session->userdata('session_id');
            $arr                   = ['active' => 'transactions_view'];
            $arr['agent_services'] = $this->M_terminal->get_agent_services();
            CI_load_view::load_view('transactions/agent_kpis', $arr);
        }
    }

    public function get_mine_kpi()
    {
        $service_id = $this->input->post('service_id');
        $agent_id   = $this->session->userdata('login');
        if ($this->db->dbdriver == 'sqlsrv') {
            $this->db->select('cast(cast(avg(cast(CAST(t.serving_time as datetime) as float)) as datetime) as time) avg_serving');
        } else {
            $this->db->select('SEC_TO_TIME(AVG(TIME_TO_SEC(t.serving_time))) as avg_serving');
        }
        $this->db->select('s.login_time as shift_start');
        $this->db->group_by('s.id,s.login_time');
        $this->db->join('sessions s', 's.id=t.session_id');
        $data = $this->db->get_where('transactions t', ['t.service_id' => $service_id, 't.status'  => 2,
            's.userID'                                                          => $agent_id, 's.user_type' => 'Agent'])->result();
        echo json_encode($data);
    }

    public function get_mine_kpi_vs_others()
    {
        $service_id = $this->input->post('service_id');
        if ($this->db->dbdriver == 'sqlsrv') {
            $this->db->select('cast(cast(avg(cast(CAST(t.serving_time as datetime) as float)) as datetime) as time) avg_serving');
        } else {
            $this->db->select('SEC_TO_TIME(AVG(TIME_TO_SEC(t.serving_time))) as avg_serving');
        }
        $this->db->select('o.name as agent_name');
        $this->db->group_by('o.id,o.name');
        $this->db->join('sessions s', 's.id = t.session_id');
        $this->db->join('Operator o', 'o.id = s.userID');
        //   $this->db->where_in('t.service_id', $agent_services_arr);
        $data = $this->db->get_where('transactions t', ['t.status' => 2, 't.service_id'      => $service_id,
            's.user_type'                                                   => 'Agent', 't.branch_id' => $this->session->userdata('Branch_id')])->result();
        echo json_encode($data);
    }

    public function get_kpi_vs_standard()
    {
        $agent_id  = $this->session->userdata('login');
        $agent_key = $this->session->userdata('agent_key');

        $this->db->select('service_id');
        $agent_services     = $this->db->get_where('agent_services', ['agent_key' => $agent_key])->result();
        $agent_services_arr = [];
        foreach ($agent_services as $service) {
            array_push($agent_services_arr, $service->service_id);
        }

        if (empty($agent_services_arr)) {
            echo json_encode([]);
            return;
        }

        if ($this->db->dbdriver == 'sqlsrv') {
            $this->db->select('cast(cast(avg(cast(CAST(t.serving_time as datetime) as float)) as datetime) as time) avg_serving');
        } else {
            $this->db->select('SEC_TO_TIME(AVG(TIME_TO_SEC(t.serving_time))) as avg_serving');
        }
        $this->db->select('ser.service_name as service_name,kpi_avg_serving_time');
        $this->db->group_by('ser.id,ser.service_name,kpi_avg_serving_time');
        $this->db->join('service ser', 'ser.id = t.service_id');
        $this->db->join('sessions s', 's.id = t.session_id AND s.userID=' . $agent_id);
        $this->db->where_in('t.service_id', $agent_services_arr);
        $data = $this->db->get_where('transactions t', ['t.status' => 2, 'kpi_avg_serving_time !=' => null,
            's.user_type'                                                   => 'Agent'])->result();
        echo json_encode($data);
    }

    public function get_current_date_time()
    {
        echo json_encode([date('d/m/Y'), date('H:i')]);
    }

    public function do_change_ticket_service($transaction_id, $service)
    {
        if ($this->session->userdata('login')) {
            date_default_timezone_set('Africa/Cairo');
            $today            = date('Y-m-d H:i:s');
            $services_num_arr = [];
            $transaction_data = $this->Crud_model->get_one('transactions', $transaction_id);
            $service_data     = $this->Crud_model->get_one('service', $service);
            if ($transaction_data) {
                $inserted_data = [
                    'serving_time'         => '00:00:00',
                    'waiting_time'         => '00:00:00',
                    'terminal_id'          => 0,
                    'client_name'          => $transaction_data->client_name,
                    'client_id_num'        => $transaction_data->client_id_num,
                    'entering_time'        => $today,
                    'service_id'           => $service_data->id,
                    'branch_id'            => $transaction_data->branch_id,
                    'date'                 => $transaction_data->date,
                    'ticket_num'           => $transaction_data->ticket_num,
                    'status'               => 0,
                    'cname'                => $transaction_data->cname,
                    'vr_service_name'      => $service_data->service_name,
                    'work_transaction_id'  => $transaction_data->work_transaction_id,
                    'work_transaction_key' => $transaction_data->work_transaction_key,
                    'order'                => $transaction_data->order,
                    'is_transfered'        => 2,
                    'transfer_details'     => $transaction_data->service_id,
                    'its_turn'             => 1,
                    'fake_its_turn'        => $transaction_data->its_turn,
                ];
                $this->Crud_model->insert('transactions', $inserted_data);
                $this->Crud_model->update('transactions', $transaction_id, ['is_transfered' => 1, 'status' => 3, 'its_turn' => 0, 'fake_its_turn' => 0]);
                record_log('Change service to: ' . $service_data->id);
                $transaction_id = $transaction_data->id;
                $w_t_id         = $transaction_data->work_transaction_id;
                $msg            = 'Change service related to ticket with number: ' . $transaction_data->ticket_num . ' at ' . date('Y-m-d H:i:s');
                row_data_log($w_t_id, $transaction_id, $transaction_data->ticket_num, $msg);
                $this->session->set_flashdata('msg', $this->lang->line('srvs_changed_successfully'));
                echo 'ok';
                //redirect(base_url() . 'Settings/transactions_view');
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            echo $error['text'];
            //$this->load->view('private/page-500', $error);
        }
    }

    public function delete_ticket($transaction_id)
    {
        if ($this->session->userdata('login')) {
            $row = $this->db->get_where('transactions', ['id' => $transaction_id])->row();
            if ($row) {
                $wt_data = $this->db->get_where('work_transactions', ['id' => $row->work_transaction_id])->row();
                $this->db->delete('transactions', ['work_transaction_id' => $row->work_transaction_id]);
                if (! empty($wt_data)) {
                    $w_t_id = $wt_data->id;
                    $msg    = 'Delete ticket at ' . date('Y-m-d H:i:s');
                    row_data_log($w_t_id, null, null, $msg);
                }
            }
            echo 'Success !!';
        } else {
            echo 'Access denied';
            return;
        }
    }

    public function load_balance_service($terminal_id = 0)
    {
        if ($this->session->userdata('login')) {
            if (get_p("terminal_lvl", "u")) {
                $terminal_data     = $this->db->get_where('terminal', ['id' => $terminal_id])->row();
                $calling_mechanism = terminal_calling_technique($terminal_data);
                if (! empty($terminal_data) && $calling_mechanism[3]) {
                    $arr['terminal_services'] = $this->M_terminal->load_balance_service($terminal_id);
                    $services                 = $this->db->get('service')->result();
                    if (! empty($services)) {
                        foreach ($services as $s) {
                            $terminal_service = $this->db->get_where('terminal_service_load_balance', ['terminal_id' => $terminal_id, 'service_id' => $s->id])->row();
                            if (! empty($terminal_service)) {
                                $s->count = $terminal_service->count;
                            } else {
                                $s->count = 0;
                            }
                        }
                    }
                    $arr['terminal_services'] = $services;
                    $arr['active']            = 'terminal';
                    $arr['terminal_id']       = $terminal_id;
                    $arr['terminal_data']     = $terminal_data;
                    CI_load_view::load_view('branch/load_balance_service', $arr);
                } else {
                    $error['text'] = $this->lang->line('permission_denied');
                    $this->load->view('private/page-500', $error);
                }
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function save_load_balance_services($terminal_id = 0)
    {
        if ($this->session->userdata('login') && get_p("terminal_lvl", "u")) {
            if ($this->input->post()) {
                $terminal_data = $this->db->get_where('terminal', ['id' => $terminal_id])->row();
                if (! empty($terminal_data)) {
                    $service = $this->db->get('service')->result();
                    if (! empty($service)) {
                        // Delete from terminal_segment_load_balance
                        $this->db->delete('terminal_service_load_balance', ['terminal_id' => $terminal_id]);
                        foreach ($service as $s) {
                            $insert = [
                                'terminal_id' => $terminal_id,
                                'service_id'  => $s->id,
                                'count'       => $this->input->post($s->id),
                            ];
                            $this->Crud_model->insert('terminal_service_load_balance', $insert);
                        }
                        redirect(base_url("C_terminal/load_balance_service/" . $terminal_id));
                    } else {
                        $error['text'] = $this->lang->line('permission_denied');
                        $this->load->view('private/page-500', $error);
                    }
                } else {
                    $error['text'] = $this->lang->line('permission_denied');
                    $this->load->view('private/page-500', $error);
                }
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function delete_current_ticket($id)
    {
        if ($this->session->userdata('login')) {
            if ($id) {
                $transaction = $this->db->get_where('transactions', ['id' => $id])->row();
                if (! empty($transaction)) {
                    $transaction_id = $transaction->id;
                    $w_t_id         = $transaction->work_transaction_id;
                    $ticket_num     = $transaction->ticket_num;
                    $msg            = 'Delete ticket num: ' . $transaction->ticket_num . ' at ' . date('Y-m-d H:i:s');
                    $this->db->delete('transactions', ['id' => $id]);
                    $this->db->delete('work_transactions', ['id' => $w_t_id]);
                    row_data_log($w_t_id, $transaction_id, $ticket_num, $msg);
                }
                echo 'ok';
                //redirect(base_url() . 'Settings/transactions_view');
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            echo $error['text'];
            //$this->load->view('private/page-500', $error);
        }
    }

    public function workstation_new($ticket_num = null, $branch = null, $terminal_wn = null)
    {
        if (! $this->require_login && ! empty($branch) && ! empty($terminal_wn)) {
            $this->session->set_userdata('branch_id', $branch);
            $this->session->set_userdata('terminal_window_no', $terminal_wn);
        }
        if ($this->session->userdata('terminal_window_no') && $this->session->userdata('branch_id') != 0) {
            $branch_id                = $this->session->userdata('branch_id');
            $arr['data']              = $this->M_terminal->get_workstation_layout_sett($branch_id);
            $arr['basic_sett']        = $this->M_settings->waiting_area_basic_sett();
            $arr['currently_serving'] = $this->M_terminal->get_currently_serving($this->session->userdata('terminal_window_no'));
            $arr['slideshow_imgs']    = $this->M_terminal->get_slideshow_imgs($branch_id);
            if (! $arr['data']) {
                $error['text'] = $this->lang->line('set_worstation_page_sett');
                $this->load->view('private/page-500', $error);
                return;
            } elseif (! is_numeric($arr['data']->refresh_rate)) {
                $error['text'] = $this->lang->line('refresh_rate_not_numeric');
                $this->load->view('private/page-500', $error);
                return;
            }
            $arr['active'] = 'workstation';
            $this->load->view('transactions/workstation_1', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function select_workstation()
    {
        if (isset($this->session->userdata("license")['lcd-based_workstation_display']) && $this->session->userdata("license")['lcd-based_workstation_display']['open'] == 1 && $this->session->userdata('User_type') == 'Agent') {
            if ($this->session->userdata('branch_id') != 0) {
                $branch_id = $this->session->userdata('branch_id');
            } elseif ($this->session->userdata('branch_id') == 0 && $this->session->userdata('updated_new_branch') != 0) {
                $branch_id = $this->session->userdata('updated_new_branch');
            } else {
                $error['text'] = $this->lang->line('error_branch_select');
                $this->load->view('private/page-500', $error);
                return;
            }
            $arr          = ['active' => 'workstation'];
            $arr['areas'] = $this->M_terminal->get_branch_workstations($branch_id);
            CI_load_view::load_view('transactions/select_workstation', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function workstation_display($waiting_area_id = null, $branch = null)
    {
        if (isset($this->session->userdata("license")['lcd-based_workstation_display']) && $this->session->userdata("license")['lcd-based_workstation_display']['open'] == 1 && $this->session->userdata('User_type') == 'Agent') {
            if (! $this->require_login && $this->session->userdata('login')) {
                $workstation = $this->db->get_where('counter', ['id' => $waiting_area_id])->row();
                if (empty($workstation)) {
                    $error['text'] = $this->lang->line('no_workstation');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                if (! $this->require_login && empty($branch)) {
                    $branch = $workstation->branch_id;
                }
                if (! $this->require_login && ! empty($branch)) {
                    $this->session->set_userdata('branch_id', $branch);
                }
                if ($this->session->userdata('branch_id') != 0) {
                    $branch_id = $this->session->userdata('branch_id');
                } elseif ($this->session->userdata('branch_id') == 0 && $this->session->userdata('updated_new_branch') != 0) {
                    $branch_id = $this->session->userdata('updated_new_branch');
                } else {
                    $error['text'] = $this->lang->line('error_branch_select');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                $arr['workstation'] = $workstation;
                $arr['data']        = $this->M_terminal->get_workstation_layout_sett($branch_id, $waiting_area_id);
                $arr['video']       = $this->M_templates->one_workstation_video(0, $branch_id, $waiting_area_id);
                $arr['sliders']     = $this->M_templates->workstation_sliders(0, $branch_id, $waiting_area_id);
                if (! $arr['data']) {
                    $error['text'] = $this->lang->line('set_waitingarea_page_sett');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                $arr['ticket']          = $this->M_terminal->get_workstation_ticket($workstation->branch_id, $workstation->number);
                $arr['active']          = 'workstation';
                $arr['branch_id']       = $branch_id;
                $arr['waiting_area_id'] = $waiting_area_id;
                $arr['basic_sett']      = $this->M_settings->waiting_area_basic_sett();
                $arr['news']            = $this->M_templates->workstation_newsbar_en(0, $branch_id, $waiting_area_id);
                $arr['news_ar']         = $this->M_templates->workstation_newsbar_ar(0, $branch_id, $waiting_area_id);
                $this->load->view('transactions/workstation', $arr);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function waiting_area_display($waiting_area_id = null, $branch = null)
    {
        if (! $this->require_login || (isset($this->session->userdata("license")['lcd-based_waiting_area_display']) && $this->session->userdata("license")['lcd-based_waiting_area_display']['open'] == 1 && (check_is_standalone() || ! check_is_server()) && get_p("waiting_area_display_lvl", "v") && $this->session->userdata('login'))) {
            $waiting_area = $this->db->get_where('waiting_areas', ['id' => $waiting_area_id])->row();
            if (empty($waiting_area)) {
                $error['text'] = $this->lang->line('no_waiting_area');
                $this->load->view('private/page-500', $error);
                return;
            }
            if (! $this->require_login && empty($branch)) {
                $branch = $waiting_area->branch_id;
            }
            if (! $this->require_login && ! empty($branch)) {
                $this->session->set_userdata('branch_id', $branch);
            }
            if ($this->session->userdata('branch_id') != 0) {
                $branch_id = $this->session->userdata('branch_id');
            } elseif ($this->session->userdata('branch_id') == 0 && $this->session->userdata('updated_new_branch') != 0) {
                $branch_id = $this->session->userdata('updated_new_branch');
            } else {
                $error['text'] = $this->lang->line('error_branch_select');
                $this->load->view('private/page-500', $error);
                return;
            }
            $arr['data']         = $this->M_terminal->get_waiting_area_settings($waiting_area_id);
            $arr['waiting_area'] = $waiting_area;
            $arr['data']         = $this->M_templates->get_waiting_area_sett_info($branch_id, $waiting_area_id);
            $arr['video']        = $this->M_templates->one_workstation_video(1, $branch_id, $waiting_area_id);
            $arr['videos']       = $this->M_templates->workstation_videos(1, $branch_id, $waiting_area_id);
            $arr['sliders']      = $this->M_templates->workstation_sliders(1, $branch_id, $waiting_area_id);
            if (! $arr['data']) {
                $error['text'] = $this->lang->line('set_waitingarea_page_sett');
                $this->load->view('private/page-500', $error);
                return;
            }
            $srvs_bar_arr = $srvs_tbl_arr = $srvs_bar_data = $srvs_tbl_data = [];
            $srvs_bar     = $this->db->get_where('waiting_area_services_bar', ['waiting_area_id' => $waiting_area_id])->result();
            if (! empty($srvs_bar)) {
                foreach ($srvs_bar as $bar) {
                    if (! in_array($bar->service_id, $srvs_bar_arr)) {
                        array_push($srvs_bar_arr, $bar->service_id);
                    }
                }
            }
            $srvs_tbl = $this->db->get_where('waiting_area_services_tbl', ['waiting_area_id' => $waiting_area_id])->result();
            if (! empty($srvs_tbl)) {
                foreach ($srvs_tbl as $tbl) {
                    if (! in_array($tbl->service_id, $srvs_tbl_arr)) {
                        array_push($srvs_tbl_arr, $tbl->service_id);
                    }
                }
            }
            $workstations           = $this->db->get_where('counter', ['branch_id' => $branch_id])->result();
            $arr['currencies']      = $this->db->get('currencies')->result();
            $arr['tickets_bar']     = $this->M_terminal->get_waiting_areas_tickets_bar($waiting_area->branch_id, $waiting_area->number, $srvs_bar_arr);
            $arr['tickets_tbl']     = $this->M_terminal->get_waiting_areas_tickets_tbl($waiting_area->branch_id, $waiting_area->number, $srvs_tbl_arr);
            $arr['serving_clients'] = $this->M_terminal->get_windows_with_serving($waiting_area_id, $branch_id, $arr['data']->sequence_of_display, $arr['data']->terminals_per_page);
            $paging_type            = $arr['data']->srv_tbl_paging_type == 1 ? 1 : 2;
            $services_tbl           = $this->M_templates->waiting_area_services_tbl_no_limit($waiting_area_id);
            if (! empty($services_tbl)) {
                foreach ($services_tbl as $s) {
                    $waiting = $this->M_templates->waiting_area_services_data($s->id, $branch_id, $paging_type);
                    if (! empty($waiting)) {
                        foreach ($waiting as $w) {
                            $w->service_name = $s->service_name;
                            $srvs_tbl_data[] = $w;
                        }
                    }
                }
            }
            $paging_type  = $arr['data']->service_paging_type == 1 ? 1 : 2;
            $services_bar = $this->M_templates->waiting_area_services_bar_no_limit($waiting_area_id);
            if (! empty($services_bar)) {
                foreach ($services_bar as $s) {
                    $waiting = $this->M_templates->waiting_area_services_data($s->id, $branch_id, $paging_type);
                    if (! empty($waiting)) {
                        foreach ($waiting as $w) {
                            $srvs_bar_data[] = $w;
                        }
                    }
                }
            }
            $arr['srvs_bar_data']   = $srvs_bar_data;
            $arr['srvs_tbl_data']   = $srvs_tbl_data;
            $arr['active']          = 'waiting_area_display';
            $arr['branch_id']       = $branch_id;
            $arr['waiting_area_id'] = $waiting_area_id;
            $arr['basic_sett']      = $this->M_settings->waiting_area_basic_sett();
            $arr['news']            = $this->M_templates->workstation_newsbar_en(1, $branch_id, $waiting_area_id);
            $arr['news_ar']         = $this->M_templates->workstation_newsbar_ar(1, $branch_id, $waiting_area_id);
            $this->load->view('transactions/waitingarea_1', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function check_updated_settings()
    {
        $branch_id       = $this->input->post('branch_id');
        $waiting_area_id = $this->input->post('waiting_area_id');
        $settings        = $this->db->select('time')->get_where('waiting_area_layout_sett', ['branch_id' => $branch_id, 'waiting_area_id' => $waiting_area_id])->row();
        echo json_encode($settings);
    }

    public function get_service_table()
    {
        $branch_id       = $this->input->post('branch_id');
        $waiting_area_id = $this->input->post('waiting_area_id');
        $data            = $this->M_templates->get_waiting_area_sett_info($branch_id, $waiting_area_id);
        $paging_type     = $data->srv_tbl_paging_type == 1 ? 1 : 2;
        $srv_tbl_d_bg    = $data->srv_tbl_d_bg ? $data->srv_tbl_d_bg : '';
        $font_size       = $data->srv_tbl_d_font_size ? $data->srv_tbl_d_font_size . 'px' : '';
        $font_family     = $data->srv_tbl_d_font_family ? $data->srv_tbl_d_font_family : '';
        $color           = $data->srv_tbl_d_font_color ? $data->srv_tbl_d_font_color : '';
        $page            = $this->input->post('page');
        $per_page        = $data->srv_tbl_page ? $data->srv_tbl_page : 1;
        $offset          = ($per_page * $page) - $per_page;
        $terminals       = explode(',', $data->srv_tbl_terminals);
        $services_id     = $terminals ? $terminals : $this->M_templates->waiting_area_services($waiting_area_id);
        $services        = $terminals ? $this->M_templates->waiting_area_services_limit_terminal($waiting_area_id, $branch_id, $terminals, $paging_type, $per_page, $offset) : $this->M_templates->waiting_area_services_limit($waiting_area_id, $branch_id, $paging_type, $per_page, $offset);
        $total_services  = $services_id ? count($services_id) : 0;
        //var_export([$services_id,$services,$total_services]);
        //die;
        if (! empty($data->srvs_tbl_max_pages)) {
            $return_page = $page >= $data->srvs_tbl_max_pages ? 1 : $page + 1;
        } else {
            // Get total of services from query
            $return_page = $page == ceil($total_services / $per_page) ? 1 : $page + 1;
        }
        $html     = '';
        $td_start = '<td class="tb-border" style="background-color: ' . $srv_tbl_d_bg . ' !important; font-size: ' . $font_size . ' !important; color: ' . $color . ' !important; font-family: ' . $font_family . ' !important;">';
        $td_end   = '</td>';
        foreach ($services as $s):
            $html .= '<tr>'
                . $td_start . ($s ? display_number_arabic($s->terminal_id, ! empty($data->srvs_tbl_arabic_numbers)) : '--') . $td_end
                . $td_start . (! empty($s->ticket_num) && $s->ticket_num !== '---' ? $s->service_name : '--------') . $td_end
                . $td_start . (! empty($s->ticket_num) && $s->ticket_num !== '---' ? display_number_arabic($s->ticket_num, ! empty($data->srvs_tbl_arabic_numbers)) : '--') . $td_end
                . '</tr>';
        endforeach;
        echo json_encode(['html' => $html, 'page' => $return_page, 't' => $services]);
    }

    public function get_service_bar()
    {
        $branch_id       = $this->input->post('branch_id');
        $waiting_area_id = $this->input->post('waiting_area_id');
        $data            = $this->M_templates->get_waiting_area_sett_info($branch_id, $waiting_area_id);
        $paging_type     = $data->service_paging_type == 1 ? 1 : 2;
        $srv_tbl_d_bg    = $data->srv_d_bg ? $data->srv_d_bg : '';
        $font_size       = $data->srv_d_font_size ? $data->srv_d_font_size . 'px' : '';
        $font_family     = $data->srv_d_font_family ? $data->srv_d_font_family : '';
        $color           = $data->srv_d_color ? $data->srv_d_color : '';
        $page            = $this->input->post('page');
        $per_page        = $data->service_page ? $data->service_page : 1;
        $offset          = ($per_page * $page) - $per_page;
        $services_id     = $this->M_templates->waiting_area_services_bar($waiting_area_id);
        $services        = $this->M_templates->waiting_area_services_bar_limit($waiting_area_id, $branch_id, $paging_type, $per_page, $offset);
                                                                  //print_r($services);die;
        $total_services = $services_id ? count($services_id) : 0; // Get total of services from query
        $return_page    = $page == ceil($total_services / $per_page) ? 1 : $page + 1;
        $html           = '';
        $td_start       = '<td style="border: 1px solid ' . $srv_tbl_d_bg . ' !important; background-color: ' . $srv_tbl_d_bg . ' !important; font-size: ' . $font_size . ' !important; color: ' . $color . ' !important; font-family: ' . $font_family . ' !important;">';
        $td_end         = '</td>';
        foreach ($services as $s):
            $html .= '<tr>'
                . $td_start . ($s ? $s->terminal_id : '--') . $td_end
                . $td_start . ($s ? $s->ticket_num : '--') . $td_end
                . '</tr>';
        endforeach;
        echo json_encode(['html' => $html, 'page' => $return_page, 't' => $services]);
    }

    public function get_currencies_table()
    {
        $branch_id       = $this->input->post('branch_id');
        $waiting_area_id = $this->input->post('waiting_area_id');
        $waiting_area    = $this->db->get_where('waiting_areas', ['id' => $waiting_area_id])->row();
        $data            = $this->M_templates->get_waiting_area_sett_info($branch_id, $waiting_area_id);
        $curr_data_bg    = $data->curr_data_bg ? $data->curr_data_bg : '';
        $font_size       = $data->curr_data_font_size ? $data->curr_data_font_size . 'px' : '';
        $font_family     = $data->curr_data_font ? $data->curr_data_font : '';
        $color           = $data->curr_data_font_color ? $data->curr_data_font_color : '';
        $page            = $this->input->post('page');
        $per_page        = $data->page_size ? $data->page_size : 1;
        $offset          = ($per_page * $page) - $per_page;
        $total_services  = 10; // Get total of services from query
        $return_page     = $page == ceil($total_services / $per_page) ? 1 : $page + 1;
        $result          = $this->M_terminal->get_currencies($per_page, $offset);
        $html            = '';
        if (! empty($result)) {
            foreach ($result as $c) {
                $title = $waiting_area->lang == 1 ? $c->n_en : $c->n_ar;
                $html .= '<tr><td style="border: 1px solid ' . $curr_data_bg . ' !important; background-color: ' . $curr_data_bg . ' !important; font-size: ' . $font_size . ' !important; color: ' . $color . ' !important; font-family: ' . $font_family . ' !important;"><span><i class="fa fa-hdd-o pull-left"></i> ' . $title . '</span></td><td style="border: 1px solid ' . $curr_data_bg . ' !important; background-color: ' . $curr_data_bg . ' !important; font-size: ' . $font_size . ' !important; color: ' . $color . ' !important; font-family: ' . $font_family . ' !important;">' . $c->bank_buy . '</td><td style="border: 1px solid ' . $curr_data_bg . ' !important; background-color: ' . $curr_data_bg . ' !important; font-size: ' . $font_size . ' !important; color: ' . $color . ' !important; font-family: ' . $font_family . ' !important;">' . $c->bank_sell . '</td><td style="border: 1px solid ' . $curr_data_bg . ' !important; background-color: ' . $curr_data_bg . ' !important; font-size: ' . $font_size . ' !important; color: ' . $color . ' !important; font-family: ' . $font_family . ' !important;">' . $c->transfer_buy . '</td><td style="border: 1px solid ' . $curr_data_bg . ' !important; background-color: ' . $curr_data_bg . ' !important; font-size: ' . $font_size . ' !important; color: ' . $color . ' !important; font-family: ' . $font_family . ' !important;">' . $c->transfer_sell . '</td></tr>';
            }
        }
        echo json_encode(['html' => $html, 'page' => $return_page]);
    }

    public function get_current_ticket()
    {
        $ticket_num      = '';
        $branch_id       = $this->input->post('branch_id');
        $waiting_area_id = $this->input->post('waiting_area_id');
        $workstation     = $this->db->get_where('counter', ['id' => $waiting_area_id])->row();
        if (! empty($workstation)) {
            $ticket = $this->M_terminal->get_workstation_ticket($branch_id, $workstation->number);
            if (! empty($ticket)) {
                $ticket_num = $ticket->ticket_num;
            }
        }
        echo json_encode(['ticket_num' => $ticket->ticket_num]);
    }

    public function check_current_transactions()
    {
        parent::login_required();
        
        // Check if agent has any current transactions being served
        $current_transactions = $this->M_terminal->get_serving_ticket();
        
        if ($current_transactions) {
            echo json_encode([$current_transactions]);
        } else {
            echo json_encode([]);
        }
    }
}
