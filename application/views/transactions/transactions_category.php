<style>
    .nav-tabs {
        margin-bottom: 20px;
        border-bottom: 2px solid #ddd;
    }
    
    .nav-tabs > li > a {
        color: #555;
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-bottom: none;
        margin-right: 2px;
        border-radius: 4px 4px 0 0;
        padding: 10px 15px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .nav-tabs > li > a:hover {
        background-color: #e9ecef;
        color: #007bff;
        border-color: #ddd;
    }
    
    .nav-tabs > li.active > a,
    .nav-tabs > li.active > a:hover,
    .nav-tabs > li.active > a:focus {
        color: #007bff;
        background-color: #fff;
        border-color: #ddd #ddd #fff;
        border-bottom: 2px solid #fff;
        font-weight: 600;
        cursor: default;
    }
    
    .tab-content {
        background-color: #fff;
        border: 1px solid #ddd;
        border-top: none;
        padding: 15px;
        border-radius: 0 0 4px 4px;
    }
    
    .tab-pane {
        min-height: 200px;
    }
</style>
<div class="row">
    <div class="col-md-12">
        <div class="panel">
            <div class="panel-header bg-primary">
                <h2><strong><?= $this->lang->line('current_transactions') ?></strong> </h2>
            </div>
            <div class="panel-content">
                <div class="row">
                    <div class="col-md-12">
                        <table class="table table-hover table-striped table-bordered table-dynamic table-tools">
                            <thead>
                                <tr>
                                    <th><?= $this->lang->line('#') ?></th>
                                    <th><?= $this->lang->line('ticket_num') ?></th>
                                    <th><?= $this->lang->line('service_name') ?></th>
                                    <th><?= $this->lang->line('entering_time') ?></th>
                                    <th><?= $this->lang->line('waiting_time') ?></th>
                                    <th><?= $this->lang->line('serving_time') ?></th>
                                    <th><?= $this->lang->line('action') ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if ($current_transactions) {
                                    $i = 1;
                                    foreach ($current_transactions as $current) {
                                        echo '<tr>';
                                        echo '<td>' . $i++ . '</td>';
                                        echo '<td>' . $current->ticket_num . '</td>';
                                        echo '<td>' . $current->vr_service_name . '</td>';
                                        echo '<td>' . date('Y-m-d H:i:s', strtotime($current->entering_time)) . '</td>';
                                        echo '<td>' . date('H:i:s', strtotime($current->waiting_time)) . '</td>';
                                        echo '<td id="current_serving_time">' . $current->calculated_serving_time . '</td>';
                                        ?>
                                    <td class="text-center">
                                        <?php if ($this->session->userdata('terminal_window_no') != 0) { ?>
                                            <?php if ($branch_terminal_data->status == 1) { ?>
                                                <?php if (get_p("show_client_workflow_lvl", "v") && get_p("show_client_workflow_lvl", "u")) { ?>
                                                    <a id="change_flow" href="<?= base_url() ?>C_workflow/change_workflow/<?= $current->ticket_num ?>" class="btn btn-info btn-sm"><?= $this->lang->line('Change_flow') ?></a>
                                                <?php } ?>
                                                <?php if ($sw_keypad_sett && $sw_keypad_sett->end_ticket) { ?>
                                                    <a id="close_current_ticket" onclick="end_current_ticket(<?= $current->id ?>)" class="btn btn-warning btn-sm"><?= $this->lang->line('close') ?></a>
                                                <?php } ?>
                                                <?php if ($sw_keypad_sett && $sw_keypad_sett->delete_ticket) { ?>
                                                    <a id="delete_current_ticket" onclick="delete_current_ticket(<?= $current->id ?>)" class="btn btn-warning btn-sm"><?= $this->lang->line('delete') ?></a>
                                                <?php } ?>
                                                <?php if ($sw_keypad_sett && $sw_keypad_sett->return_to_queue) { ?>
                                                    <a id="return_to_queue" onclick="return_to_queue(<?= $current->id ?>)" class="btn btn-success btn-sm"><?= $this->lang->line('return_to_queue') ?></a>
                                                <?php } ?>
                                                <?php if ($sw_keypad_sett && $sw_keypad_sett->transfer_exist_customers) { ?>
                                                    <a id="change_current_service" onclick="change_ticket_service(<?= $current->id . ',' . $current->service_id ?>)" class="btn btn-warning btn-sm"><?= $this->lang->line('trans_srv') ?></a>
                                                    <a id="transfer_ticket" onclick="transfer_ticket(<?= $current->id ?>)" class="btn btn-warning btn-sm"><?= $this->lang->line('trans_term') ?></a>
                                                <?php } ?>
                                                <?php if ($config && $config->enable_millensys && $config->millensys_url) { ?>
                                                    <a id="request_millensys" onclick="millensys_ticket(<?= $current->id ?>)" class="btn btn-success btn-sm"><?= $this->lang->line('request_millensys') ?></a>
                                                <?php } ?>
                                                <?php if ($sw_keypad_sett && $sw_keypad_sett->hold_ticket) { ?>
                                                    <a id="hold_current_ticket" onclick="Hold_ticket(<?= $current->id ?>)" class="btn btn-danger btn-sm" style="margin-top:4px"><?= $this->lang->line('hold') ?></a>
                                                    <?php
                                                }
                                            }
                                        }
                                        ?>
                                    </td>
                                    <?php
                                    echo '</tr>';
                                }
                            }
                            ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- hold tickets -->
<div class="row" id="holding_tickets_container"  <?= !$holding_transactions ? 'style="display:none;"' : '' ?>>
    <div class="col-md-12">
        <div class="panel">
            <div class="panel-header bg-primary">
                <h2><strong><?= $this->lang->line('hold_transactions') ?></strong> </h2>
            </div>
            <div class="panel-content">
                <div class="row text-center">

                    <div class="col-md-12">
                        <table class="table table-hover table-striped table-bordered table-dynamic table-tools">
                            <thead>
                                <tr>
                                    <th><?= $this->lang->line('#') ?></th>
                                    <th><?= $this->lang->line('ticket_num') ?></th>
                                    <th><?= $this->lang->line('service_name') ?></th>
<!--                                            <th><?//= $this->lang->line('serving_time') ?></th> -->
                                    <th><?= $this->lang->line('entering_time') ?></th>
                                    <!--<th><?//= $this->lang->line('waiting_time') ?></th>-->
                                    <th><?= $this->lang->line('action') ?></th>
                                </tr>
                            </thead>
                            <tbody id="holding_transations">
                                <?php
                                if ($holding_transactions) {
                                    $k = 1;
                                    foreach ($holding_transactions as $transaction) {
                                        echo '<tr>';
                                        echo '<td>' . $k++ . '</td>';
                                        echo '<td>' . $transaction->ticket_num . '</td>';
                                        echo '<td>' . $transaction->vr_service_name . '</td>';
//                                                echo '<td>' . $transaction->serving_time . '</td>';
                                        echo '<td>' . date('Y-m-d H:i:s', strtotime($transaction->entering_time)) . '</td>';
//                                                echo '<td>' . $transaction->resuming_time . '</td>';
                                        ?>
                                    <td class="text-center">
                                        <?php
                                        if ($this->session->userdata('terminal_window_no') != 0) {
                                            if ($sw_keypad_sett && $sw_keypad_sett->hold_ticket && !$current_transactions) {
                                                ?>
                                                <a id="resume_current_ticket" onclick="Resume_ticket(<?= $transaction->id ?>)" class="btn btn-success btn-sm"><?= $this->lang->line('resume') ?></a>
                                                <?php
                                            }
                                        }
                                        ?>
                                    </td>
                                    <?php
                                    echo '</tr>';
                                }
                            }
                            ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Waiting tickets -->
<div class="row">
    <div class="col-md-12">
        <div class="panel">
            <div class="panel-header bg-primary">
                <h2><strong><?= $this->lang->line('waiting_transactions') ?></strong> </h2>
            </div>
            <div class="panel-content">
                <div class="row text-center">
                    <?php
                    if ($this->session->userdata('terminal_window_no') && $this->session->userdata('terminal_window_no') != 0) {
                        if ($branch_terminal_data->status == 1 && $branch_terminal_data->idle_state == 1 && ($calling_mechanism[1] || $calling_mechanism[2] || $calling_mechanism[3] || $calling_mechanism[4])) {
                            if ($sw_keypad_sett && $sw_keypad_sett->next_auto_call) {
                                ?>
                                <a id="next_btn" title="Next (Enter)" onclick="serve_ticket(0)" class="btn btn-success btn-md"><?= $this->lang->line('next') ?></a>
                                <?php
                            }
                        }
                    }
                    ?>
                    <div class="col-md-12">
                        <!-- Service Tabs -->
                        <ul class="nav nav-tabs" id="serviceTabs" role="tablist">
                            <!-- Tabs will be dynamically generated here -->
                        </ul>
                        
                        <!-- Tab Content -->
                        <div class="tab-content" id="serviceTabContent">
                            <!-- Tab panes will be dynamically generated here -->
                        </div>
                    </div> 
                </div>
            </div>
        </div>
    </div>
</div>