<style>
    .control-label{
        width: 16% !important;
    }
    
    /* Style for disabled next button */
    #next_btn.disabled,
    #next_btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        pointer-events: none;
    }
</style>
<div class="modal fade in" id="modal-basic">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52"></i></button>
                <h4 class="modal-title"><strong id='modal-title'>Success</strong> </h4>
            </div>
            <div class="modal-body" id='modal-body'>
                <?= $this->lang->line('op_success') ?> <br>
            </div>
            <div class="modal-footer" id='modal-footer'>
                <button type="button" class="btn btn-default btn-embossed" data-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>
<!-- For Transfer Ticket -->
<div class="modal fade in" id="transfer_ticket">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52"></i></button>
                <h4 class="modal-title"><strong id='transfer_ticket_title'>Success</strong> </h4>
            </div>
            <div class="modal-body" id='transfer_ticket_body'>
                <div class="row">
                    <div class="col-md-12 text-center">
                        <div class="form-group">
                            <select class="form-control" id="branch_terminals" data-search="true">
                                <option value=""><?= $this->lang->line('select_terminal') ?></option>
                                <?php
                                if ($terminals_except_mine) {
                                    foreach ($terminals_except_mine as $terminal) {
                                        ?>
                                        <option value="<?= $terminal->window_no ?>"><?= $this->lang->line('terminal') . ' ' . $terminal->window_no ?></option>
                                        <?php
                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" id='transfer_ticket_footer'>
                <button type="button" class="btn btn-default btn-embossed" data-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>
<!-- For Millensys Ticket -->
<div class="modal fade in" id="millensys_ticket">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52"></i></button>
                <h4 class="modal-title"><strong id='millensys_ticket_title'>Success</strong> </h4>
            </div>
            <div class="modal-body" id='millensys_ticket_body'>
                <div class="row">
                    <div class="col-md-12 text-center">
                        <div class="form-group">
                            <select class="form-control" id="millensys_service" data-search="true">
                                <option value=""><?= $this->lang->line('select_service') ?></option>
                                <?php
                                if ($millensys_services) {
                                    foreach ($millensys_services as $service) {
                                        ?>
                                        <option value="<?= $service->id ?>"><?= $service->arabic_service_name ?></option>
                                        <?php
                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" id='millensys_ticket_footer'>
                <button type="button" class="btn btn-default btn-embossed" data-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>
<!-- Update agent services modal -->
<div class="modal fade in" id="change_service">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52"></i></button>
                <h4 class="modal-title"><strong id='change_service_title'>Success</strong> </h4>
            </div>
            <div class="modal-body" id='change_service_body'>
                <div class="row">
                    <div class="col-md-12 text-center">
                        <div class="form-group">
                            <select multiple class="form-control" id="services" name="services[]" data-search="true">

                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" id='change_service_footer'>
                <button type="button" class="btn btn-default btn-embossed" data-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>
<!-- Change service modal -->
<div class="modal fade in" id="change_ticket_service">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52"></i></button>
                <h4 class="modal-title"><strong id='change_ticket_service_title'>Success</strong> </h4>
            </div>
            <div class="modal-body" id='change_ticket_service_body'>
                <div class="row">
                    <div class="col-md-12 text-center">
                        <div class="form-group">
                            <select class="form-control" id="ticket_service" name="ticket_service" data-search="true">
                                <option value=""><?= $this->lang->line('select_service') ?></option>
                                <?php
                                if ($services) {
                                    foreach ($services as $service) {
                                        ?>
                                        <option value="<?= $service->id ?>"><?= $service->arabic_service_name ?></option>
                                        <?php
                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" id='change_ticket_service_footer'>
                <button type="button" class="btn btn-default btn-embossed" data-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>
<div class="main-content">
    <?php $this->load->view('private/header'); ?>
    <div class="page-content">
        <?php if ($this->session->flashdata('msg')) { ?>
            <div class="row">
                <div class="col-md-12">                                        
                    <div class="alert alert-block alert-success fade in">
                        <a class="close" data-dismiss="alert" href="#" aria-hidden="true">×</a>
                        <p></p><h4><i class="fa fa-check"></i> <?= $this->lang->line('lang_success') ?></h4>
                        <?= $this->session->flashdata('msg') ?>                                                
                        <p></p>
                    </div>                                       
                </div>
            </div>
        <?php } ?>
        <?php if ($this->session->flashdata('warning')) { ?>
            <div class="row">
                <div class="col-md-12">                                        
                    <div class="alert alert-block alert-warning fade in text-center">
                        <a class="close" data-dismiss="alert" href="#" aria-hidden="true">×</a>
                        <?= $this->session->flashdata('warning') ?>                                                
                    </div>                                       
                </div>
            </div>
        <?php } ?>
        <div id='warning_msg'>
            <ul id="noty_center_layout_container" class="error_msg_ul i-am-new" >
                <li class="made noty_container_type_success animated bounceIn" style="width: 310px; cursor: pointer; height: 86px;">
                    <div class="noty_bar noty_type_success" id="noty_1197577781054546000">
                        <div class="noty_message">
                            <span class="noty_text">
                                <div class="alert media fade in alert-warning">
                                    <p><strong><?= $this->lang->line('warning') ?></strong> <span id="warning_text"></span> </p><br/>
                                    <span class="error_dismiss_text"><?= $this->lang->line('click_to_dismiss') ?></span>
                                </div>
                            </span>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <div id='error_msg'>
            <ul id="noty_center_layout_container" class="error_msg_ul i-am-new" >
                <li class="made noty_container_type_success animated bounceIn" style="width: 310px; cursor: pointer; height: 86px;">
                    <div class="noty_bar noty_type_success" id="noty_1197577781054546000">
                        <div class="noty_message">
                            <span class="noty_text">
                                <div class="alert media fade in alert-danger">
                                    <p><strong><?= $this->lang->line('error') ?></strong> <span id="error_text"></span> </p><br/>
                                    <span class="error_dismiss_text"><?= $this->lang->line('click_to_dismiss') ?></span>
                                </div>
                            </span>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <!-- Current tickets -->
        <div class="row">
            <div class="col-md-12 text-center">
                <?php
                if ($this->session->userdata('terminal_window_no') && $this->session->userdata('terminal_window_no') != 0) {
                    if ($branch_terminal_data->idle_state == 1) {
                        if (!$current_transactions) {
                            ?>
                            <a onclick="set_workstation_status('<?= $branch_terminal_data->id ?>', 0)" class="btn btn-success btn-md"><?= $this->lang->line('set_workstation_idle') ?></a>
                            <?php
                        }
                    } elseif ($branch_terminal_data->idle_state == 0) {
                        ?>
                        <a onclick="set_workstation_status('<?= $branch_terminal_data->id ?>', 1)" class="btn btn-danger btn-md"><?= $this->lang->line('set_workstation_active') ?></a>
                        <?php
                    }
                }
                ?>
            </div>
            <?php if ($this->session->userdata('User_type') == 'Agent' || get_p("agent_kpis_lvl", "v")) { ?>
                <div class="pull-right" style="padding-right: 15px">
                    <a onclick="location.href = '<?= base_url() ?>C_terminal/agent_kpis'" target="_blank" class="btn btn-success btn-md"><i class="glyphicon glyphicon-stats"></i> &nbsp;<?= $this->lang->line('my_kpis') ?></a>
                </div>
                <?php
                $calling = '';
                if ($calling_mechanism[0]) {
                    $calling .= $this->lang->line('adhoc');
                }
                if ($calling_mechanism[1]) {
                    $calling .= $calling ? ' - ' : '';
                    $calling .= $this->lang->line('fcfs');
                }
                if ($calling_mechanism[2]) {
                    $calling .= $calling ? ' - ' : '';
                    $calling .= $this->lang->line('priority_based');
                }
                if ($calling_mechanism[3]) {
                    $calling .= $calling ? ' - ' : '';
                    $calling .= $this->lang->line('load_balance');
                }
                if ($calling_mechanism[4]) {
                    $calling .= $calling ? ' - ' : '';
                    $calling .= $this->lang->line('reservation_based');
                }
                if (!$calling) {
                    $calling = 'Not defined';
                }
                ?>
                <h3 class="pull-left" style="margin-left:15px;margin-bottom: 0px;color: #666666"><?= $this->lang->line('calling_mechanism') . ' : ' . $calling ?></h3>
            <?php } ?>
        </div>
        <div id="system_transactions">
            <?php $this->load->view('transactions/transactions_category'); ?>
        </div>
        <?php $this->load->view('private/footer'); ?>
    </div>
</div>
</div>
</div>
</div>
</section>
<input type="hidden" id="ticket_num" >
<!--<script src="<?php echo base_url(); ?>assets/js/workstation_serving.js"></script>-->
<script src="<?= base_url() ?>assets/plugins/moment/moment.min.js" ></script>
<script>
var next = false;
var lastActivity = Date.now(); // Track last activity
var logoutTimer = null; // Auto logout timer
var idleTime = <?= !empty($sw_keypad_sett->idle_time) ? $sw_keypad_sett->idle_time * 1000 : 300000 ?>; // Convert to milliseconds, default 5 minutes

$.xhrPool = [];
$.xhrPool.abortAll = function() {
  _.each(this, function(jqXHR) {
    jqXHR.abort();
  });
};
$.ajaxSetup({
  beforeSend: function(jqXHR) {
    $.xhrPool.push(jqXHR);
  }
});
                    shortcut = {all_shortcuts: {}, add: function (e, t, a) {
                            var r = {type: "keydown", propagate: !1, disable_in_input: !1, target: document, keycode: !1};
                            if (a)
                                for (var n in r)
                                    "undefined" == typeof a[n] && (a[n] = r[n]);
                            else
                                a = r;
                            var s = a.target;
                            "string" == typeof a.target && (s = document.getElementById(a.target));
                            e = (e || '').toLowerCase();
                            var o = function (r) {
                                if (r = r || window.event, a.disable_in_input) {
                                    var n;
                                    if (r.target ? n = r.target : r.srcElement && (n = r.srcElement), 3 == n.nodeType && (n = n.parentNode), "INPUT" == n.tagName || "TEXTAREA" == n.tagName)
                                        return
                                }
                                r.keyCode ? code = r.keyCode : r.which && (code = r.which);
                                var s = String.fromCharCode(code).toLowerCase();
                                188 == code && (s = ","), 190 == code && (s = ".");
                                var o = e.split("+"), d = 0, c = {"`": "~", 1: "!", 2: "@", 3: "#", 4: "$", 5: "%", 6: "^", 7: "&", 8: "*", 9: "(", 0: ")", "-": "_", "=": "+", ";": ":", "'": '"', ",": "<", ".": ">", "/": "?", "\\": "|"}, l = {esc: 27, escape: 27, tab: 9, space: 32, "return": 13, enter: 13, backspace: 8, scrolllock: 145, scroll_lock: 145, scroll: 145, capslock: 20, caps_lock: 20, caps: 20, numlock: 144, num_lock: 144, num: 144, pause: 19, "break": 19, insert: 45, home: 36, "delete": 46, end: 35, pageup: 33, page_up: 33, pu: 33, pagedown: 34, page_down: 34, pd: 34, left: 37, up: 38, right: 39, down: 40, f1: 112, f2: 113, f3: 114, f4: 115, f5: 116, f6: 117, f7: 118, f8: 119, f9: 120, f10: 121, f11: 122, f12: 123}, p = {shift: {wanted: !1, pressed: !1}, ctrl: {wanted: !1, pressed: !1}, alt: {wanted: !1, pressed: !1}, meta: {wanted: !1, pressed: !1}};
                                r.ctrlKey && (p.ctrl.pressed = !0), r.shiftKey && (p.shift.pressed = !0), r.altKey && (p.alt.pressed = !0), r.metaKey && (p.meta.pressed = !0);
                                for (var i = 0; k = o[i], i < o.length; i++)
                                    "ctrl" == k || "control" == k ? (d++, p.ctrl.wanted = !0) : "shift" == k ? (d++, p.shift.wanted = !0) : "alt" == k ? (d++, p.alt.wanted = !0) : "meta" == k ? (d++, p.meta.wanted = !0) : k.length > 1 ? l[k] == code && d++ : a.keycode ? a.keycode == code && d++ : s == k ? d++ : c[s] && r.shiftKey && (s = c[s], s == k && d++);
                                return d != o.length || p.ctrl.pressed != p.ctrl.wanted || p.shift.pressed != p.shift.wanted || p.alt.pressed != p.alt.wanted || p.meta.pressed != p.meta.wanted || (t(r), a.propagate) ? void 0 : (r.cancelBubble = !0, r.returnValue = !1, r.stopPropagation && (r.stopPropagation(), r.preventDefault()), !1)
                            };
                            this.all_shortcuts[e] = {callback: o, target: s, event: a.type}, s.addEventListener ? s.addEventListener(a.type, o, !1) : s.attachEvent ? s.attachEvent("on" + a.type, o) : s["on" + a.type] = o
                        }, remove: function (e) {
                            e = (e || '').toLowerCase();
                            var t = this.all_shortcuts[e];
                            if (delete this.all_shortcuts[e], t) {
                                var a = t.event, r = t.target, n = t.callback;
                                r.detachEvent ? r.detachEvent("on" + a, n) : r.removeEventListener ? r.removeEventListener(a, n, !1) : r["on" + a] = !1
                            }
                        }};
                    $(document).ready(function () {
                        $("#next_btn").hide();
                        
                        // Initialize auto logout functionality
                        initAutoLogout();
                        
                        // Next BTN
                        if ('<?= $sw_keypad_sett && $sw_keypad_sett->next_auto_call && $sw_keypad_sett->next_hot_key != '' ?>') {
                            shortcut.add('<?= $sw_keypad_sett->next_hot_key ?>', function () {
                                $("#next_btn").trigger('click');
                            }, {'type': 'keydown', 'propagate': false, 'target': document});
                        }
                        // Close BTN
                        if ('<?= $sw_keypad_sett && $sw_keypad_sett->end_ticket && $sw_keypad_sett->close_hot_key != '' ?>') {
                            shortcut.add('<?= $sw_keypad_sett->close_hot_key ?>', function () {
                                $("#close_current_ticket").trigger('click');
                            }, {'type': 'keydown', 'propagate': false, 'target': document});
                        }
                        // Change Service BTN
                        if ('<?= $sw_keypad_sett && $sw_keypad_sett->transfer_exist_customers && $sw_keypad_sett->change_srv_hot_key != '' ?>') {
                            shortcut.add('<?= $sw_keypad_sett->change_srv_hot_key ?>', function () {
                                $("#change_current_service").trigger('click');
                            }, {'type': 'keydown', 'propagate': false, 'target': document});
                        }
                        // Hold Customer BTN
                        if ('<?= $sw_keypad_sett && $sw_keypad_sett->hold_ticket && $sw_keypad_sett->hold_hot_key != '' ?>') {
                            shortcut.add('<?= $sw_keypad_sett->hold_hot_key ?>', function () {
                                $("#hold_current_ticket").trigger('click');
                            }, {'type': 'keydown', 'propagate': false, 'target': document});
                        }
                        // Delete Ticket BTN
                        if ('<?= $sw_keypad_sett && $sw_keypad_sett->delete_ticket && $sw_keypad_sett->delete_hot_key != '' ?>') {
                            shortcut.add('<?= $sw_keypad_sett->delete_hot_key ?>', function () {
                                $("#delete_current_ticket").trigger('click');
                            }, {'type': 'keydown', 'propagate': false, 'target': document});
                        }
                        // Return To Queue BTN
                        if ('<?= $sw_keypad_sett && $sw_keypad_sett->return_to_queue && $sw_keypad_sett->return_hot_key != '' ?>') {
                            shortcut.add('<?= $sw_keypad_sett->return_hot_key ?>', function () {
                                $("#return_to_queue").trigger('click');
                            }, {'type': 'keydown', 'propagate': false, 'target': document});
                        }
                        $("#modal-basic").hide();
                        $("#error_msg").hide();
                        $("#warning_msg").hide();
<?php if ($holding_transactions) { ?>
                            $("#holding_tickets_container").show();
<?php } else { ?>
                            $("#holding_tickets_container").hide();
<?php } ?>                        setInterval(function () {
                            // Waiting tickets
                            var csrf_test_name = get_cookie('csrf_cookie_name');
                            if(!next) {
                                $.ajax({
                                    url: "<?php echo base_url() ?>Settings/get_waiting_transaction",
                                    type: "POST",
                                    data: {'<?= $this->security->get_csrf_token_name() ?>': csrf_test_name},
                                    dataType: 'JSON',
                                    success: function (data) {                                        if (data != null) {
                                            // Get the currently active tab to preserve selection
                                            var activeTab = localStorage.getItem('activeServiceTab') || 'all';
                                            
                                            // Clear existing tabs and content
                                            $("#serviceTabs").html('');
                                            $("#serviceTabContent").html('');
                                            
                                            // Group data by service_id
                                            var serviceGroups = {};
                                            for (var i = 0; i < data.length; i++) {
                                                var serviceId = data[i].service_id;
                                                var serviceName = data[i].service_name || data[i].vr_service_name;
                                                
                                                if (!serviceGroups[serviceId]) {
                                                    serviceGroups[serviceId] = {
                                                        name: serviceName,
                                                        tickets: []
                                                    };
                                                }
                                                serviceGroups[serviceId].tickets.push(data[i]);
                                            }
                                            
                                            // Create "All" tab first
                                            var isAllActive = (activeTab === 'all') ? 'active' : '';
                                            $("#serviceTabs").append('<li class="' + isAllActive + '">' +
                                                '<a data-toggle="tab" href="#service-all" role="tab" onclick="setActiveTab(\'all\')">' +
                                                    'All (' + data.length + ')' +
                                                '</a>' +
                                            '</li>');
                                            
                                            // Create "All" tab content with all tickets
                                            var allTabContent = '<div class="tab-pane ' + isAllActive + '" id="service-all">' +
                                                '<table class="table table-hover table-striped table-bordered">' +
                                                    '<thead>' +
                                                        '<tr>' +
                                                            '<th><?= $this->lang->line('#') ?></th>' +
                                                            '<th><?= $this->lang->line('ticket_num') ?></th>' +
                                                            '<th><?= $this->lang->line('service_name') ?></th>' +
                                                            '<th><?= $this->lang->line('entering_time') ?></th>' +
                                                            '<th><?= $this->lang->line('waiting_time') ?></th>' +
                                                            '<th><?= $this->lang->line('action') ?></th>' +
                                                        '</tr>' +
                                                    '</thead>' +
                                                    '<tbody>';
                                            
                                            // Add all tickets to the "All" tab
                                            for (var i = 0; i < data.length; i++) {
                                                var ticket = data[i];
                                                var add_remove_ticket = '';
    <?php if ($sw_keypad_sett && $sw_keypad_sett->save_customers) { ?>
                                                if (ticket.terminal_id == <?= $this->session->userdata('terminal_window_no') ?>) {
                                                    add_remove_ticket = '<a onclick="add_remove_ticket_to_terminal(' + ticket.id + ', 0)" class="btn btn-warning btn-sm"><?= $this->lang->line('remove_ticket') ?></a>';
                                                } else {
                                                    add_remove_ticket = '<a onclick="add_remove_ticket_to_terminal(' + ticket.id + ', 1)" class="btn btn-warning btn-sm"><?= $this->lang->line('add_ticket') ?></a>';
                                                }
    <?php } ?>
                                                
                                                allTabContent += '<tr>' +
                                                    '<td>' + (i + 1) + '</td>' +
                                                    '<td>' + ticket.ticket_num + '</td>' +
                                                    '<td>' + ticket.vr_service_name + '</td>' +
                                                    '<td>' + ticket.entering_time + '</td>' +
                                                    '<td id="waiting_client_time_' + ticket.branch_id + '_' + ticket.id + '">' + ticket.calculated_waiting_time + '</td>' +
                                                    '<td class="text-center">' +
    <?php if ($this->session->userdata('terminal_window_no') != 0) { ?>
        <?php if ($branch_terminal_data->status == 1 && $branch_terminal_data->idle_state == 1) { ?>
            <?php if ($sw_keypad_sett && $sw_keypad_sett->save_customers) { ?>
                <?php if ($calling_mechanism[0]) { ?>
                                                        '<a onclick="serve_ticket(' + ticket.id + ')" class="btn btn-warning btn-sm"><?= $this->lang->line('serve') ?></a>' +
                <?php } ?>
            <?php } ?>
            <?php if ($sw_keypad_sett && $sw_keypad_sett->delete_ticket) { ?>
                                                        '<a id="delete_current_ticket" onclick="delete_current_ticket(' + ticket.id + ')" class="btn btn-warning btn-sm"><?= $this->lang->line('delete') ?></a>' +
            <?php } ?>
                                                        add_remove_ticket +
            <?php
        }
    }
    ?>
                                                    '</td>' +
                                                '</tr>';
                                            }
                                            allTabContent += '</tbody></table></div>';
                                            $("#serviceTabContent").append(allTabContent);
                                            
                                            var tabIndex = 0;
                                              // Create tabs and content for each service
                                            for (var serviceId in serviceGroups) {
                                                var service = serviceGroups[serviceId];
                                                var isActive = (activeTab === serviceId) ? 'active' : '';
                                                  // Create tab
                                                $("#serviceTabs").append('<li class="' + isActive + '">' +
                                                    '<a data-toggle="tab" href="#service-' + serviceId + '" role="tab" onclick="setActiveTab(\'' + serviceId + '\')">' +
                                                        service.name + ' (' + service.tickets.length + ')' +
                                                    '</a>' +
                                                '</li>');
                                                  // Create tab content
                                                var tabContent = '<div class="tab-pane ' + isActive + '" id="service-' + serviceId + '">' +
                                                    '<table class="table table-hover table-striped table-bordered">' +
                                                        '<thead>' +
                                                            '<tr>' +
                                                                '<th><?= $this->lang->line('#') ?></th>' +
                                                                '<th><?= $this->lang->line('ticket_num') ?></th>' +
                                                                '<th><?= $this->lang->line('service_name') ?></th>' +
                                                                '<th><?= $this->lang->line('entering_time') ?></th>' +
                                                                '<th><?= $this->lang->line('waiting_time') ?></th>' +
                                                                '<th><?= $this->lang->line('action') ?></th>' +
                                                            '</tr>' +
                                                        '</thead>' +
                                                        '<tbody id="waiting_transactions_' + serviceId + '">';
                                                
                                                // Add tickets for this service
                                                for (var j = 0; j < service.tickets.length; j++) {
                                                    var ticket = service.tickets[j];
                                                    var add_remove_ticket = '';
                                                    var terminal = <?= $this->session->userdata('terminal_window_no') ?>;
    <?php if ($sw_keypad_sett && $sw_keypad_sett->save_customers) { ?>
                                                    if (ticket.terminal_id == <?= $this->session->userdata('terminal_window_no') ?>) {
                                                        add_remove_ticket = '<a onclick="add_remove_ticket_to_terminal(' + ticket.id + ', 0)" class="btn btn-warning btn-sm"><?= $this->lang->line('remove_ticket') ?></a>';
                                                    } else {
                                                        add_remove_ticket = '<a onclick="add_remove_ticket_to_terminal(' + ticket.id + ', 1)" class="btn btn-warning btn-sm"><?= $this->lang->line('add_ticket') ?></a>';
                                                    }
    <?php } ?>
                                                    
                                                    tabContent += '<tr>' +
                                                        '<td>' + (j + 1) + '</td>' +
                                                        '<td>' + ticket.ticket_num + '</td>' +
                                                        '<td>' + ticket.vr_service_name + '</td>' +
                                                        '<td>' + ticket.entering_time + '</td>' +
                                                        '<td id="waiting_client_time_' + ticket.branch_id + '_' + ticket.id + '">' + ticket.calculated_waiting_time + '</td>' +
                                                        '<td class="text-center">' +
    <?php if ($this->session->userdata('terminal_window_no') != 0) { ?>
        <?php if ($branch_terminal_data->status == 1 && $branch_terminal_data->idle_state == 1) { ?>
            <?php if ($sw_keypad_sett && $sw_keypad_sett->save_customers) { ?>
                <?php if ($calling_mechanism[0]) { ?>
                                                            '<a onclick="serve_ticket(' + ticket.id + ')" class="btn btn-warning btn-sm"><?= $this->lang->line('serve') ?></a>' +
                <?php } ?>
            <?php } ?>
            <?php if ($sw_keypad_sett && $sw_keypad_sett->delete_ticket) { ?>
                                                            '<a id="delete_current_ticket" onclick="delete_current_ticket(' + ticket.id + ')" class="btn btn-warning btn-sm"><?= $this->lang->line('delete') ?></a>' +
            <?php } ?>
                                                            add_remove_ticket +
            <?php
        }
    }
    ?>
                                                        '</td>' +
                                                    '</tr>';
                                                }
                                                  tabContent += '</tbody></table></div>';
                                                $("#serviceTabContent").append(tabContent);
                                                
                                                tabIndex++;
                                            }
                                            
                                            // Show next button if there are tickets
                                            if (data.length > 0) {
                                                $("#next_btn").show();
                                            }
    <?php if ($sw_keypad_sett && $sw_keypad_sett->show_pop_up_notif == 1) { ?>
                                            highlight_alarm();
    <?php } ?>
                                        }
                                    }
                                });
                        }

                            // current serving time counter
                            var time_plus_min = moment.utc($("#current_serving_time").text(), 'HH:mm:ss').add(3, 'second').format('HH:mm:ss');
                            $("#current_serving_time").text(time_plus_min);
                        }, 5000);
                        /*******Start Check for auto next*******/
                        setInterval(function () {
                            var csrf_test_name = get_cookie('csrf_cookie_name');
                            if(!next) {
                                $.ajax({
                                    url: "<?php echo base_url() ?>Settings/get_sw_keypad_sett",
                                    type: "POST",
                                    data: {'<?= $this->security->get_csrf_token_name() ?>': csrf_test_name},
                                    dataType: 'JSON',
                                    success: function (data) {
                                        if (data == true) {
                                            $("#next_btn").click();
                                        }
                                    }
                                });
                            }
                        }, <?= !empty($sw_keypad_sett->idle_time) ? $sw_keypad_sett->idle_time * 1000 : 5000 ?>);
                        /*******End check for auto next*******/
                        //BEGIN NEXT SHORTCUT
<?php
if ($this->session->userdata('terminal_window_no') && $this->session->userdata('terminal_window_no') != 0 && is_array($waiting_transactions) && count($waiting_transactions) > 0) {
    if ($branch_terminal_data->status == 1) {
        ?>
                                $('body').keypress(function (e) {
                                    var key = e.which;
                                    if (key == 13)  // the enter key code
                                    {
                                        serve_ticket(0);
                                    }
                                });
        <?php
    }
}
?>
                        $('body').click(function () {
                            $("#error_msg").hide();
                            $("#warning_msg").hide();
                        });                        //END NEXT SHORTCUT
                    });
                    
                    // Function to handle tab switching and localStorage
                    function setActiveTab(tabId) {
                        localStorage.setItem('activeServiceTab', tabId);
                    }
                    
                    function serve_ticket(id) {
                        // Disable next button to prevent multiple calls
                        $("#next_btn").prop('disabled', true).addClass('disabled');
                        
                        var csrf_test_name = get_cookie('csrf_cookie_name');
                        next = true;
                        
                        // Reset activity and logout timer on ticket serving
                        resetActivity();
                        
                        $.xhrPool.abortAll;
                        $.ajax({
                            url: "<?php echo base_url() ?>C_terminal/serve_ticket/" + id,
                            //dataType: 'JSON',
                            data: {'<?= $this->security->get_csrf_token_name() ?>': csrf_test_name},
                            success: function (data) {
                                if (data == 'ok') {
                                    update_view();
                                }
                                next = false;
                                // Re-enable next button after successful response
                                $("#next_btn").prop('disabled', false).removeClass('disabled');
                            },
                            error: function(xhr) {
                                next = false;
                                // Re-enable next button on error
                                $("#next_btn").prop('disabled', false).removeClass('disabled');
                            }
                        });
                        //location.href = "<?php echo base_url(); ?>C_terminal/serve_ticket/" + id;
                    }
                    function Hold_ticket(id) {
                        var csrf_test_name = get_cookie('csrf_cookie_name');
                        
                        // Reset activity when interacting with tickets
                        resetActivity();
                        
                        $.ajax({
                            url: "<?php echo base_url() ?>C_terminal/Hold_ticket/" + id,
                            //dataType: 'JSON',
                            data: {'<?= $this->security->get_csrf_token_name() ?>': csrf_test_name},
                            success: function (data) {
                                if (data == 'ok') {
                                    update_view();
                                }
                            }
                        });
                        //window.location = "<?php echo base_url(); ?>C_terminal/Hold_ticket/" + id;
                    }

                    function return_to_queue(id) {
                        var csrf_test_name = get_cookie('csrf_cookie_name');
                        $.ajax({
                            url: "<?php echo base_url() ?>C_terminal/retuen_to_queue_ticket/" + id,
                            //dataType: 'JSON',
                            data: {'<?= $this->security->get_csrf_token_name() ?>': csrf_test_name},
                            success: function (data) {
                                if (data == 'ok') {
                                    update_view();
                                }
                            }
                        });
                        //window.location = "<?php echo base_url(); ?>C_terminal/retuen_to_queue_ticket/" + id;
                    }
                    function Resume_ticket(id) {
                        var csrf_test_name = get_cookie('csrf_cookie_name');
                        $.ajax({
                            url: "<?php echo base_url() ?>C_terminal/Resume_ticket/" + id,
                            //dataType: 'JSON',
                            data: {'<?= $this->security->get_csrf_token_name() ?>': csrf_test_name},
                            success: function (data) {
                                if (data == 'ok') {
                                    update_view();
                                }
                            }
                        });
                        //window.location = "<?php echo base_url(); ?>C_terminal/Resume_ticket/" + id;
                    }
                    function end_current_ticket(id) {
                        var csrf_test_name = get_cookie('csrf_cookie_name');
                        
                        // Reset activity when interacting with tickets
                        resetActivity();
                        
                        $.ajax({
                            url: "<?php echo base_url() ?>C_terminal/end_current_ticket/" + id,
                            //dataType: 'JSON',
                            data: {'<?= $this->security->get_csrf_token_name() ?>': csrf_test_name},
                            success: function (data) {
                                if (data == 'ok') {
                                    update_view();
                                }
                            }
                        });
                        //window.location = "<?php echo base_url(); ?>C_terminal/end_current_ticket/" + id;
                    }
                    function millensys_ticket(transaction_id) {
                        $("#millensys_ticket_ticket_title").html("<?= $this->lang->line('millensys_ticket') ?>");
                        $("#millensys_ticket_footer").html('<button type="button" onclick="do_millensys_ticket(' + transaction_id + ')" class="btn btn-default btn-embossed" ><?= $this->lang->line("yes") ?></button></a>');
                        $("#millensys_ticket_footer").append('<button type="button" class="btn btn-default btn-embossed" data-dismiss="modal"><?= $this->lang->line("no") ?></button>');
                        $("#millensys_ticket").modal('show');
                    }
                    function do_millensys_ticket(transaction_id) {
                        var millensys_service = $("#millensys_service").val();
                        if (millensys_service != '') {
                            window.open("<?php echo base_url(); ?>C_terminal/do_millensys_ticket/" + transaction_id + "/" + millensys_service, '_blank');
                        } else {
                            alert('Specify service...');
                        }
                    }
                    function transfer_ticket(transaction_id) {
                        $("#transfer_ticket_title").html("<?= $this->lang->line('transfer_ticket') ?>");
                        $("#transfer_ticket_footer").html('<button type="button" onclick="do_transfer_ticket(' + transaction_id + ')" class="btn btn-default btn-embossed" ><?= $this->lang->line("yes") ?></button></a>');
                        $("#transfer_ticket_footer").append('<button type="button" class="btn btn-default btn-embossed" data-dismiss="modal"><?= $this->lang->line("no") ?></button>');
                        $("#transfer_ticket").modal('show');
                    }
                    function do_transfer_ticket(transaction_id) {
                        var branch_terminal = $("#branch_terminals").val();
                        if (branch_terminal != '') {
                            var csrf_test_name = get_cookie('csrf_cookie_name');
                            $.ajax({
                                url: "<?php echo base_url() ?>C_terminal/do_transfer_ticket/" + transaction_id + "/" + branch_terminal,
                                dataType: 'JSON',
                                data: {'<?= $this->security->get_csrf_token_name() ?>': csrf_test_name},
                                success: function (data) {
                                    if (data == 'ok') {
                                        update_view();
                                    }
                                }
                            });
                            //window.location = "<?php echo base_url(); ?>C_terminal/do_transfer_ticket/" + transaction_id + "/" + branch_terminal;
                        } else {
                            alert('Specify terminal...');
                        }
                    }
                    function delete_ticket(transaction_id) {
                        var r = confirm("Are you sure you want to delete ticket!");
                        if (r === true) {
                            $('.loader-overlay').removeClass('loaded');
                            var csrf_test_name = get_cookie('csrf_cookie_name');
                            $.ajax({
                                url: "<?php echo base_url() ?>C_terminal/delete_ticket/" + transaction_id,
                                //dataType: 'JSON',
                                data: {'<?= $this->security->get_csrf_token_name() ?>': csrf_test_name},
                                success: function (data) {
                                    $('.loader-overlay').addClass('loaded');
                                    if (data == 'Success !!') {
                                        update_view();
                                    }
                                }
                            });
                        }
                    }
                    function update_view() {
                        $('.loader-overlay').removeClass('loaded');
                        var csrf_test_name = get_cookie('csrf_cookie_name');
                        $.ajax({
                            url: "<?php echo base_url() ?>Settings/transactions_ajax",
                            dataType: 'JSON',
                            data: {'<?= $this->security->get_csrf_token_name() ?>': csrf_test_name},
                            success: function (data) {
                                $('.loader-overlay').addClass('loaded');
                                if (data.success) {
                                    $("#system_transactions").html(data.html);
                                }
                            },
                            error: function (xhr) {
                                $('.loader-overlay').addClass('loaded');
                            }
                        });
                    }
                    function set_workstation_status(terminal_id, status) {
                        window.location = "<?php echo base_url(); ?>C_terminal/set_terminal_idle_active/" + terminal_id + "/" + status;
                    }

                    function add_remove_ticket_to_terminal(transaction_id, status) {
                        window.location = "<?php echo base_url(); ?>C_terminal/add_remove_ticket/" + transaction_id + "/" + status;
                    }

                    function msToTime(duration) {
                        var milliseconds = parseInt((duration % 1000) / 100)
                                , seconds = parseInt((duration / 1000) % 60)
                                , minutes = parseInt((duration / (1000 * 60)) % 60)
                                , hours = parseInt((duration / (1000 * 60 * 60)) % 24);
                        hours = (hours < 10) ? "0" + hours : hours;
                        minutes = (minutes < 10) ? "0" + minutes : minutes;
                        seconds = (seconds < 10) ? "0" + seconds : seconds;
                        return hours + ":" + minutes + ":" + seconds + "." + milliseconds;
                    }
                    function highlight_alarm() {
                        if(!next) {
                            var csrf_test_name = get_cookie('csrf_cookie_name');
                            $.ajax({
                                url: "<?php echo base_url() ?>C_alarm/check_login",
                                data: {'<?= $this->security->get_csrf_token_name() ?>': csrf_test_name},
                                success: function (data) {
                                    if (data == 'still_login') {
                                        var csrf_test_name = get_cookie('csrf_cookie_name');
                                        $.ajax({
                                            url: "<?php echo base_url() ?>C_alarm/check_alarm_transaction_page",
                                            dataType: 'JSON',
                                            data: {'<?= $this->security->get_csrf_token_name() ?>': csrf_test_name},
                                            success: function (data) {
                                                $("#error_text").html('');
                                                $("#warning_text").html('');
                                                $("#error_msg").hide();
                                                $("#warning_msg").hide();
                                                //waiting per service
                                                if (data[1] && data[1].length > 0) {
                                                    $("#error_text").append(data[1][0][0]);
                                                } else if (data[6] && data[6].length > 0) {
                                                    $("#warning_text").append(data[6][0][0]);
                                                }

                                                //idle
                                                if (data[2] && data[2].length > 0) {
                                                    $("#error_text").append('<br/> You have been in idle state for a long time.');
                                                } else if (data[7] && data[7].length > 0) {
                                                    $("#warning_text").append('<br/> You have been in idle state for a long time.');
                                                }
                                                if (data[1] && data[1].length > 0) {
                                                    $("#error_msg").show();
                                                } else if (data[6] && data[6].length > 0) {
                                                    $("#warning_msg").show();
                                                }
                                                if (data[2] && data[2].length > 0) {
                                                    $("#error_msg").show();
                                                } else if (data[7] && data[7].length > 0) {
                                                    $("#warning_msg").show();
                                                }


                                                if (data[8] && data[8].length > 0) {
													// waiting
													for (var j = 0; j < data[8].length; j++) {
														$("#" + data[8][j][1]).removeClass('red-text');
														$("#" + data[8][j][1]).removeClass('green-text');
														$("#" + data[8][j][1]).addClass('orange-text');
														$("#" + data[8][j][1]).addClass('orange-text');
													}
												}
                                                if (data[3] && data[3].length > 0) {
													for (var j = 0; j < data[3].length; j++) {
														$("#" + data[3][j][1]).removeClass('orange-text');
														$("#" + data[3][j][1]).removeClass('green-text');
														$("#" + data[3][j][1]).addClass('red-text');
													}
                                                }
                                                //serving
                                                if (data[9] && data[9].length > 0) {
                                                    $("#current_serving_time").removeClass('red-text');
                                                    $("#current_serving_time").removeClass('green-text');
                                                    $("#current_serving_time").addClass('orange-text');
                                                }
                                                if (data[4] && data[4].length > 0) {
                                                    $("#current_serving_time").removeClass('orange-text');
                                                    $("#current_serving_time").removeClass('green-text');
                                                    $("#current_serving_time").addClass('red-text');
                                                }


                                            }
                                        });
                                    }
                                }
                            });
                        }
                    }
                    function add_remove_service() {
                        $("#change_service_title").html("<?= $this->lang->line('add_remove_service') ?>");
                        $("#change_service_footer").html('<button type="button" onclick="do_add_remove_service()" class="btn btn-default btn-embossed" ><?= $this->lang->line("yes") ?></button></a>');
                        $("#change_service_footer").append('<button type="button" class="btn btn-default btn-embossed" data-dismiss="modal"><?= $this->lang->line("no") ?></button>');
                        $("#change_service").modal('show');
                    }
                    function do_add_remove_service() {
                        var agent_services = $("#services").val();
                        var csrf_test_name = get_cookie('csrf_cookie_name');
                        $.ajax({
                            url: '<?= base_url() ?>Settings/do_add_remove_service',
                            type: "POST",
                            data: {
                                agent_services: agent_services,
                                '<?= $this->security->get_csrf_token_name() ?>': csrf_test_name
                            },
                            dataType: "JSON",
                            success: function (success) {
                                update_view();
                                //location.reload();
                            }
                        });
                    }
                    function change_ticket_service(transaction_id, srv_id) {
                        $("#ticket_service").empty();
                        var content = "";
                        $.ajax({
                            url: '<?= base_url() ?>Settings/get_branch_services',
                            type: "POST",
                            dataType: "json",
                            async: false,
                            data: {
                                srv_id: srv_id
                            },
                            success: function (items) {
                                content = '<option value=""><?= $this->lang->line('select_service') ?></option>';
                                $.each(items, function (index, items) {
                                    content += '<option value=' + items.id + '>' + items.arabic_service_name + '</option>';
                                });
                            }
                        });
                        var html = content;
                        $("#ticket_service").append(html);
                        $("#ticket_service").select2();
                        $("#change_ticket_service_title").html("<?= $this->lang->line('change_service') ?>");
                        $("#change_ticket_service_footer").html('<button type="button" onclick="do_change_ticket_service(' + transaction_id + ')" class="btn btn-default btn-embossed" ><?= $this->lang->line("yes") ?></button></a>');
                        $("#change_ticket_service_footer").append('<button type="button" class="btn btn-default btn-embossed" data-dismiss="modal"><?= $this->lang->line("no") ?></button>');
                        $("#change_ticket_service").modal('show');
                    }
                    function do_change_ticket_service(transaction_id) {
                        var service = $("#ticket_service").val();
                        if (service != '') {
                            var csrf_test_name = get_cookie('csrf_cookie_name');
                            $.ajax({
                                url: "<?php echo base_url() ?>C_terminal/do_change_ticket_service/" + transaction_id + "/" + service,
                                //dataType: 'JSON',
                                data: {'<?= $this->security->get_csrf_token_name() ?>': csrf_test_name},
                                success: function (data) {
                                    if (data == 'ok') {
                                        update_view();
                                    }
                                }
                            });
                            //window.location = "<?php echo base_url(); ?>C_terminal/do_change_ticket_service/" + transaction_id + "/" + service;
                        } else {
                            alert('Specify Service...');
                        }
                    }
                    function delete_current_ticket(id) {
                        var csrf_test_name = get_cookie('csrf_cookie_name');
                        $.ajax({
                            url: "<?php echo base_url() ?>C_terminal/delete_current_ticket/" + id,
                            //dataType: 'JSON',
                            data: {'<?= $this->security->get_csrf_token_name() ?>': csrf_test_name},
                            success: function (data) {
                                if (data == 'ok') {
                                    update_view();
                                }
                            }
                        });
                        //window.location = "<?php echo base_url(); ?>C_terminal/delete_current_ticket/" + id;
                    }
                    
                    // Auto logout functionality
                    function initAutoLogout() {
                        // Track user activity
                        $(document).on('click keypress mousemove', function() {
                            resetActivity();
                        });
                        
                        // Start the logout timer
                        startLogoutTimer();
                    }
                    
                    function resetActivity() {
                        lastActivity = Date.now();
                        if (logoutTimer) {
                            clearTimeout(logoutTimer);
                        }
                        startLogoutTimer();
                    }
                    
                    function startLogoutTimer() {
                        logoutTimer = setTimeout(function() {
                            checkForIdleLogout();
                        }, idleTime);
                    }
                    
                    function checkForIdleLogout() {
                        // Check if agent has any current transactions being served
                        var csrf_test_name = get_cookie('csrf_cookie_name');
                        $.ajax({
                            url: "<?php echo base_url() ?>C_terminal/check_current_transactions",
                            type: "POST",
                            data: {'<?= $this->security->get_csrf_token_name() ?>': csrf_test_name},
                            dataType: 'JSON',
                            success: function (data) {
                                // If no current transactions, proceed with logout
                                if (!data || data.length === 0) {
                                    performAutoLogout();
                                } else {
                                    // If agent has current transactions, reset timer
                                    resetActivity();
                                }
                            },
                            error: function() {
                                // On error, don't logout to be safe
                                resetActivity();
                            }
                        });
                    }
                    
                    function performAutoLogout() {
                        // Clear any running timers
                        if (logoutTimer) {
                            clearTimeout(logoutTimer);
                        }
                        
                        // Show logout message
                        alert('<?= $this->lang->line('auto_logout_message') ?? 'You have been automatically logged out due to inactivity.' ?>');
                        
                        // Redirect to logout page
                        window.location.href = '<?= base_url() ?>C_login/logout';
                    }
</script>